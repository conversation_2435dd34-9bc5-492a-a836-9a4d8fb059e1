// =====================================================
// ORDER SERVICE
// خدمة الطلبات والمعاملات
// =====================================================

import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../../core/config/supabase_config.dart';
import '../../../../core/services/database_service.dart';

class OrderService {
  // =====================================================
  // ORDER MANAGEMENT
  // إدارة الطلبات
  // =====================================================

  /// Get restaurant orders
  static Future<Map<String, dynamic>> getRestaurantOrders({
    required String restaurantId,
    List<String>? statusFilter,
    String? dateFrom,
    String? dateTo,
    String? searchQuery,
    int? limit,
    int? offset,
  }) async {
    try {
      Map<String, dynamic> filters = {'restaurant_id': restaurantId};
      Map<String, dynamic>? greaterThan;
      Map<String, dynamic>? lessThan;

      // Apply status filter
      Map<String, List<dynamic>>? inFilters;
      if (statusFilter != null && statusFilter.isNotEmpty) {
        inFilters = {'status': statusFilter};
      }

      // Apply date filters
      if (dateFrom != null) {
        greaterThan = {'created_at': dateFrom};
      }
      if (dateTo != null) {
        lessThan = {'created_at': dateTo};
      }

      final response = await DatabaseService.getWithFilters(
        table: SupabaseConfig.ordersTable,
        select: '''
          *,
          users (
            id,
            full_name,
            phone,
            email
          ),
          drivers (
            id,
            full_name,
            phone,
            current_location
          ),
          order_items (
            id,
            quantity,
            unit_price,
            total_price,
            special_instructions,
            selected_options,
            meals (
              id,
              name,
              image
            )
          ),
          payments (
            id,
            amount,
            payment_method,
            status,
            created_at
          )
        ''',
        equals: filters,
        inFilters: inFilters,
        greaterThan: greaterThan,
        lessThan: lessThan,
        orderBy: 'created_at',
        ascending: false,
        limit: limit,
      );

      // Apply search filter if provided
      if (searchQuery != null && searchQuery.isNotEmpty && response['success']) {
        final orders = response['data'] as List;
        final filteredOrders = orders.where((order) {
          final orderNumber = order['order_number']?.toString().toLowerCase() ?? '';
          final customerName = order['customer_name']?.toString().toLowerCase() ?? '';
          final customerPhone = order['customer_phone']?.toString().toLowerCase() ?? '';
          final query = searchQuery.toLowerCase();
          
          return orderNumber.contains(query) || 
                 customerName.contains(query) || 
                 customerPhone.contains(query);
        }).toList();

        response['data'] = filteredOrders;
      }

      return response;
    } catch (e) {
      return SupabaseConfig.errorResponse(
        message: 'فشل في جلب الطلبات',
        error: e.toString(),
      );
    }
  }

  /// Get single order details
  static Future<Map<String, dynamic>> getOrderDetails(String orderId) async {
    try {
      final response = await DatabaseService.read(
        table: SupabaseConfig.ordersTable,
        select: '''
          *,
          users (
            id,
            full_name,
            phone,
            email,
            address
          ),
          restaurants (
            id,
            name,
            phone,
            address
          ),
          drivers (
            id,
            full_name,
            phone,
            current_location,
            vehicle_type,
            vehicle_plate
          ),
          order_items (
            id,
            quantity,
            unit_price,
            total_price,
            special_instructions,
            selected_options,
            meals (
              id,
              name,
              description,
              image,
              price
            )
          ),
          payments (
            id,
            amount,
            payment_method,
            status,
            transaction_id,
            created_at
          )
        ''',
        filters: {'id': orderId},
      );

      if (response['success'] && response['data'].isNotEmpty) {
        return SupabaseConfig.successResponse(
          data: response['data'][0],
          message: 'تم جلب تفاصيل الطلب بنجاح',
        );
      } else {
        return SupabaseConfig.errorResponse(
          message: 'لم يتم العثور على الطلب',
        );
      }
    } catch (e) {
      return SupabaseConfig.errorResponse(
        message: 'فشل في جلب تفاصيل الطلب',
        error: e.toString(),
      );
    }
  }

  /// Create new order
  static Future<Map<String, dynamic>> createOrder({
    required String restaurantId,
    String? userId,
    String? customerName,
    String? customerPhone,
    String? customerEmail,
    required String deliveryAddress,
    required List<Map<String, dynamic>> orderItems,
    required double subtotal,
    required double deliveryFee,
    required double totalPrice,
    String paymentMethod = 'cash',
    String orderType = 'delivery',
    String? notes,
    Map<String, dynamic>? deliveryLocation,
  }) async {
    try {
      // Generate order number
      final orderNumber = await _generateOrderNumber();

      // Create order
      final orderData = {
        'order_number': orderNumber,
        'restaurant_id': restaurantId,
        'user_id': userId,
        'customer_name': customerName,
        'customer_phone': customerPhone,
        'customer_email': customerEmail,
        'delivery_address': deliveryAddress,
        'delivery_location': deliveryLocation,
        'subtotal': subtotal,
        'delivery_fee': deliveryFee,
        'total_price': totalPrice,
        'payment_method': paymentMethod,
        'payment_status': 'pending',
        'order_type': orderType,
        'status': 'pending',
        'notes': notes,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      final orderResponse = await DatabaseService.create(
        table: SupabaseConfig.ordersTable,
        data: orderData,
      );

      if (!orderResponse['success']) {
        return orderResponse;
      }

      final orderId = orderResponse['data']['id'];

      // Create order items
      final orderItemsData = orderItems.map((item) => {
        'order_id': orderId,
        'meal_id': item['meal_id'],
        'quantity': item['quantity'],
        'unit_price': item['unit_price'],
        'total_price': item['total_price'],
        'special_instructions': item['special_instructions'],
        'selected_options': item['selected_options'] ?? {},
        'created_at': DateTime.now().toIso8601String(),
      }).toList();

      await DatabaseService.batchInsert(
        table: SupabaseConfig.orderItemsTable,
        data: orderItemsData,
      );

      // Create payment record
      await DatabaseService.create(
        table: SupabaseConfig.paymentsTable,
        data: {
          'order_id': orderId,
          'amount': totalPrice,
          'payment_method': paymentMethod,
          'status': 'pending',
          'created_at': DateTime.now().toIso8601String(),
        },
      );

      return SupabaseConfig.successResponse(
        data: {
          'order_id': orderId,
          'order_number': orderNumber,
          'total_price': totalPrice,
        },
        message: 'تم إنشاء الطلب بنجاح',
      );
    } catch (e) {
      return SupabaseConfig.errorResponse(
        message: 'فشل في إنشاء الطلب',
        error: e.toString(),
      );
    }
  }

  /// Update order status
  static Future<Map<String, dynamic>> updateOrderStatus({
    required String orderId,
    required String newStatus,
    String? notes,
    int? estimatedTime,
  }) async {
    try {
      Map<String, dynamic> updateData = {
        'status': newStatus,
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (notes != null) {
        updateData['notes'] = notes;
      }

      if (estimatedTime != null) {
        final estimatedDeliveryTime = DateTime.now().add(Duration(minutes: estimatedTime));
        updateData['estimated_delivery_time'] = estimatedDeliveryTime.toIso8601String();
      }

      // Special handling for specific statuses
      switch (newStatus) {
        case 'confirmed':
          updateData['confirmed_at'] = DateTime.now().toIso8601String();
          break;
        case 'preparing':
          updateData['preparation_started_at'] = DateTime.now().toIso8601String();
          break;
        case 'ready':
          updateData['ready_at'] = DateTime.now().toIso8601String();
          break;
        case 'picked_up':
          updateData['picked_up_at'] = DateTime.now().toIso8601String();
          break;
        case 'delivered':
          updateData['delivered_at'] = DateTime.now().toIso8601String();
          updateData['actual_delivery_time'] = DateTime.now().toIso8601String();
          break;
        case 'cancelled':
          updateData['cancelled_at'] = DateTime.now().toIso8601String();
          break;
      }

      final response = await DatabaseService.update(
        table: SupabaseConfig.ordersTable,
        data: updateData,
        filters: {'id': orderId},
      );

      if (response['success']) {
        // Add order tracking record
        await _addOrderTracking(orderId, newStatus, notes);

        // Send notification to customer
        await _sendOrderNotification(orderId, newStatus);
      }

      return response;
    } catch (e) {
      return SupabaseConfig.errorResponse(
        message: 'فشل في تحديث حالة الطلب',
        error: e.toString(),
      );
    }
  }

  /// Accept order
  static Future<Map<String, dynamic>> acceptOrder({
    required String orderId,
    int estimatedTime = 30,
  }) async {
    return await updateOrderStatus(
      orderId: orderId,
      newStatus: 'confirmed',
      notes: 'تم قبول الطلب من قبل المطعم',
      estimatedTime: estimatedTime,
    );
  }

  /// Reject order
  static Future<Map<String, dynamic>> rejectOrder({
    required String orderId,
    String? reason,
  }) async {
    return await updateOrderStatus(
      orderId: orderId,
      newStatus: 'cancelled',
      notes: reason ?? 'تم رفض الطلب من قبل المطعم',
    );
  }

  // =====================================================
  // HELPER METHODS
  // الطرق المساعدة
  // =====================================================

  /// Generate unique order number
  static Future<String> _generateOrderNumber() async {
    final now = DateTime.now();
    final dateStr = '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}';
    final timeStr = '${now.hour.toString().padLeft(2, '0')}${now.minute.toString().padLeft(2, '0')}${now.second.toString().padLeft(2, '0')}';
    return 'ORD$dateStr$timeStr';
  }

  /// Add order tracking record
  static Future<void> _addOrderTracking(String orderId, String status, String? notes) async {
    try {
      await DatabaseService.create(
        table: 'order_tracking',
        data: {
          'order_id': orderId,
          'status': status,
          'notes': notes,
          'created_at': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      print('Error adding order tracking: $e');
    }
  }

  /// Send order notification
  static Future<void> _sendOrderNotification(String orderId, String status) async {
    try {
      // Get order details to find user
      final orderResponse = await DatabaseService.read(
        table: SupabaseConfig.ordersTable,
        select: 'user_id, customer_name',
        filters: {'id': orderId},
      );

      if (orderResponse['success'] && orderResponse['data'].isNotEmpty) {
        final order = orderResponse['data'][0];
        final userId = order['user_id'];

        if (userId != null) {
          final statusMessages = {
            'confirmed': 'تم تأكيد طلبك وبدء التحضير',
            'preparing': 'جاري تحضير طلبك',
            'ready': 'طلبك جاهز للاستلام',
            'picked_up': 'تم استلام طلبك من المطعم',
            'on_the_way': 'طلبك في الطريق إليك',
            'delivered': 'تم تسليم طلبك بنجاح',
            'cancelled': 'تم إلغاء طلبك',
          };

          final message = statusMessages[status] ?? 'تم تحديث حالة طلبك';

          await DatabaseService.create(
            table: SupabaseConfig.notificationsTable,
            data: {
              'user_id': userId,
              'title': 'تحديث الطلب',
              'message': message,
              'type': 'order',
              'data': {'order_id': orderId, 'status': status},
              'is_read': false,
              'created_at': DateTime.now().toIso8601String(),
            },
          );
        }
      }
    } catch (e) {
      print('Error sending order notification: $e');
    }
  }

  // =====================================================
  // REAL-TIME SUBSCRIPTIONS
  // الاشتراكات الفورية
  // =====================================================

  /// Subscribe to restaurant orders
  static RealtimeChannel subscribeToRestaurantOrders({
    required String restaurantId,
    required Function(Map<String, dynamic>) onNewOrder,
    required Function(Map<String, dynamic>) onOrderUpdate,
  }) {
    return DatabaseService.subscribeToTable(
      table: SupabaseConfig.ordersTable,
      onInsert: (payload) {
        final newRecord = payload.newRecord;
        if (newRecord['restaurant_id'] == restaurantId) {
          onNewOrder(newRecord);
        }
      },
      onUpdate: (payload) {
        final newRecord = payload.newRecord;
        if (newRecord['restaurant_id'] == restaurantId) {
          onOrderUpdate(newRecord);
        }
      },
      onDelete: (payload) {
        // Handle order deletion if needed
      },
    );
  }
}
