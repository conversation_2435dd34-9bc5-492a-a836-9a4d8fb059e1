import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/services/test_auth_service.dart';

class TestAuthScreen extends StatefulWidget {
  const TestAuthScreen({super.key});

  @override
  State<TestAuthScreen> createState() => _TestAuthScreenState();
}

class _TestAuthScreenState extends State<TestAuthScreen> {
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isLoading = false;
  String _result = '';

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _testConnection() async {
    setState(() {
      _isLoading = true;
      _result = 'جاري اختبار الاتصال...';
    });

    final result = await TestAuthService.testConnection();
    
    setState(() {
      _isLoading = false;
      _result = 'نتيجة اختبار الاتصال:\n${result.toString()}';
    });
  }

  Future<void> _testSignUp() async {
    if (_emailController.text.isEmpty || _passwordController.text.isEmpty) {
      setState(() {
        _result = 'يرجى إدخال البريد الإلكتروني وكلمة المرور';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _result = 'جاري إنشاء الحساب...';
    });

    final result = await TestAuthService.simpleSignUp(
      email: _emailController.text.trim(),
      password: _passwordController.text,
    );
    
    setState(() {
      _isLoading = false;
      _result = 'نتيجة إنشاء الحساب:\n${result.toString()}';
    });
  }

  Future<void> _testSignIn() async {
    if (_emailController.text.isEmpty || _passwordController.text.isEmpty) {
      setState(() {
        _result = 'يرجى إدخال البريد الإلكتروني وكلمة المرور';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _result = 'جاري تسجيل الدخول...';
    });

    final result = await TestAuthService.simpleSignIn(
      email: _emailController.text.trim(),
      password: _passwordController.text,
    );
    
    setState(() {
      _isLoading = false;
      _result = 'نتيجة تسجيل الدخول:\n${result.toString()}';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'اختبار المصادقة',
          style: GoogleFonts.inter(
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Connection Test
            ElevatedButton(
              onPressed: _isLoading ? null : _testConnection,
              child: Text('اختبار الاتصال مع Supabase'),
            ),
            
            const SizedBox(height: 24),
            
            // Email Field
            TextField(
              controller: _emailController,
              decoration: const InputDecoration(
                labelText: 'البريد الإلكتروني',
                hintText: '<EMAIL>',
                prefixIcon: Icon(Icons.email),
              ),
              keyboardType: TextInputType.emailAddress,
            ),
            
            const SizedBox(height: 16),
            
            // Password Field
            TextField(
              controller: _passwordController,
              decoration: const InputDecoration(
                labelText: 'كلمة المرور',
                hintText: '123456',
                prefixIcon: Icon(Icons.lock),
              ),
              obscureText: true,
            ),
            
            const SizedBox(height: 24),
            
            // Sign Up Test
            ElevatedButton(
              onPressed: _isLoading ? null : _testSignUp,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.success,
              ),
              child: Text('اختبار إنشاء حساب'),
            ),
            
            const SizedBox(height: 16),
            
            // Sign In Test
            ElevatedButton(
              onPressed: _isLoading ? null : _testSignIn,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.info,
              ),
              child: Text('اختبار تسجيل دخول'),
            ),
            
            const SizedBox(height: 24),
            
            // Loading Indicator
            if (_isLoading)
              const Center(
                child: CircularProgressIndicator(),
              ),
            
            const SizedBox(height: 16),
            
            // Result Display
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppColors.greyLight,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppColors.inputBorder),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _result.isEmpty ? 'النتائج ستظهر هنا...' : _result,
                    style: GoogleFonts.robotoMono(
                      fontSize: 14,
                    ),
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Back to Login
            TextButton(
              onPressed: () {
                Navigator.of(context).pushReplacementNamed('/login');
              },
              child: Text(
                'العودة لشاشة تسجيل الدخول',
                style: GoogleFonts.inter(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
