// =====================================================
// RESTAURANT PROVIDER
// مزود بيانات المطعم مع Supabase
// =====================================================

import 'package:flutter/material.dart';
import '../../data/services/restaurant_service.dart';
import '../../../../core/services/auth_service.dart';

class RestaurantProvider with ChangeNotifier {
  // =====================================================
  // STATE VARIABLES
  // متغيرات الحالة
  // =====================================================

  Map<String, dynamic>? _restaurant;
  List<Map<String, dynamic>> _categories = [];
  List<Map<String, dynamic>> _meals = [];
  Map<String, dynamic>? _statistics;
  
  bool _isLoading = false;
  String? _error;

  // =====================================================
  // GETTERS
  // الحصول على البيانات
  // =====================================================

  Map<String, dynamic>? get restaurant => _restaurant;
  List<Map<String, dynamic>> get categories => _categories;
  List<Map<String, dynamic>> get meals => _meals;
  Map<String, dynamic>? get statistics => _statistics;
  bool get isLoading => _isLoading;
  String? get error => _error;

  bool get isRestaurantOpen => _restaurant?['is_open'] ?? false;
  bool get isRestaurantActive => _restaurant?['is_active'] ?? false;
  String get restaurantName => _restaurant?['name'] ?? 'مطعم غير محدد';
  double get restaurantRating => _restaurant?['rating']?.toDouble() ?? 0.0;

  // =====================================================
  // RESTAURANT MANAGEMENT
  // إدارة المطعم
  // =====================================================

  /// Initialize restaurant data
  Future<void> initializeRestaurant() async {
    try {
      _setLoading(true);
      _setError(null);

      final user = AuthService.getCurrentUser();
      if (user == null) {
        _setError('المستخدم غير مسجل الدخول');
        return;
      }

      // Get restaurant data
      final restaurantResponse = await RestaurantService.getRestaurantByUserId(user.id);
      if (restaurantResponse['success']) {
        _restaurant = restaurantResponse['data'];
        
        // Load related data
        await Future.wait([
          loadCategories(),
          loadMeals(),
          loadStatistics(),
        ]);
      } else {
        _setError(restaurantResponse['message']);
      }
    } catch (e) {
      _setError('خطأ في تحميل بيانات المطعم: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Load categories
  Future<void> loadCategories() async {
    try {
      final response = await RestaurantService.getCategories();
      if (response['success']) {
        _categories = List<Map<String, dynamic>>.from(response['data']);
        notifyListeners();
      }
    } catch (e) {
      print('Error loading categories: $e');
    }
  }

  /// Load meals
  Future<void> loadMeals({
    String? categoryId,
    bool? isAvailable,
    String? searchQuery,
  }) async {
    if (_restaurant == null) return;

    try {
      final response = await RestaurantService.getRestaurantMeals(
        restaurantId: _restaurant!['id'],
        categoryId: categoryId,
        isAvailable: isAvailable,
        searchQuery: searchQuery,
      );

      if (response['success']) {
        _meals = List<Map<String, dynamic>>.from(response['data']);
        notifyListeners();
      }
    } catch (e) {
      print('Error loading meals: $e');
    }
  }

  /// Load statistics
  Future<void> loadStatistics({String period = 'today'}) async {
    if (_restaurant == null) return;

    try {
      final response = await RestaurantService.getRestaurantStatistics(
        restaurantId: _restaurant!['id'],
        period: period,
      );

      if (response['success']) {
        _statistics = response['data'];
        notifyListeners();
      }
    } catch (e) {
      print('Error loading statistics: $e');
    }
  }

  /// Toggle restaurant status
  Future<bool> toggleRestaurantStatus() async {
    if (_restaurant == null) return false;

    try {
      _setLoading(true);
      
      final newStatus = !isRestaurantOpen;
      final response = await RestaurantService.toggleRestaurantStatus(
        restaurantId: _restaurant!['id'],
        isOpen: newStatus,
      );

      if (response['success']) {
        _restaurant!['is_open'] = newStatus;
        notifyListeners();
        return true;
      } else {
        _setError(response['message']);
        return false;
      }
    } catch (e) {
      _setError('خطأ في تحديث حالة المطعم: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Update restaurant information
  Future<bool> updateRestaurant(Map<String, dynamic> data) async {
    if (_restaurant == null) return false;

    try {
      _setLoading(true);
      
      final response = await RestaurantService.updateRestaurant(
        restaurantId: _restaurant!['id'],
        data: data,
      );

      if (response['success']) {
        _restaurant = response['data'];
        notifyListeners();
        return true;
      } else {
        _setError(response['message']);
        return false;
      }
    } catch (e) {
      _setError('خطأ في تحديث بيانات المطعم: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // =====================================================
  // MENU MANAGEMENT
  // إدارة القائمة
  // =====================================================

  /// Add new meal
  Future<bool> addMeal({
    required String categoryId,
    required String name,
    required String description,
    required double price,
    String? nameEn,
    String? descriptionEn,
    double? originalPrice,
    int discountPercentage = 0,
    List<String>? ingredients,
    List<String>? allergens,
    Map<String, dynamic>? nutritionalInfo,
    int preparationTime = 15,
    int? calories,
    bool isAvailable = true,
    bool isFeatured = false,
    bool isSpicy = false,
    bool isVegetarian = false,
    bool isVegan = false,
    bool isHalal = true,
    List<String>? tags,
  }) async {
    if (_restaurant == null) return false;

    try {
      _setLoading(true);
      
      final response = await RestaurantService.addMeal(
        restaurantId: _restaurant!['id'],
        categoryId: categoryId,
        name: name,
        description: description,
        price: price,
        nameEn: nameEn,
        descriptionEn: descriptionEn,
        originalPrice: originalPrice,
        discountPercentage: discountPercentage,
        ingredients: ingredients,
        allergens: allergens,
        nutritionalInfo: nutritionalInfo,
        preparationTime: preparationTime,
        calories: calories,
        isAvailable: isAvailable,
        isFeatured: isFeatured,
        isSpicy: isSpicy,
        isVegetarian: isVegetarian,
        isVegan: isVegan,
        isHalal: isHalal,
        tags: tags,
      );

      if (response['success']) {
        // Reload meals to get updated list
        await loadMeals();
        return true;
      } else {
        _setError(response['message']);
        return false;
      }
    } catch (e) {
      _setError('خطأ في إضافة الوجبة: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Update meal
  Future<bool> updateMeal(String mealId, Map<String, dynamic> data) async {
    try {
      _setLoading(true);
      
      final response = await RestaurantService.updateMeal(
        mealId: mealId,
        data: data,
      );

      if (response['success']) {
        // Update meal in local list
        final mealIndex = _meals.indexWhere((meal) => meal['id'] == mealId);
        if (mealIndex != -1) {
          _meals[mealIndex] = {..._meals[mealIndex], ...data};
          notifyListeners();
        }
        return true;
      } else {
        _setError(response['message']);
        return false;
      }
    } catch (e) {
      _setError('خطأ في تحديث الوجبة: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Delete meal
  Future<bool> deleteMeal(String mealId) async {
    try {
      _setLoading(true);
      
      final response = await RestaurantService.deleteMeal(mealId);

      if (response['success']) {
        // Remove meal from local list
        _meals.removeWhere((meal) => meal['id'] == mealId);
        notifyListeners();
        return true;
      } else {
        _setError(response['message']);
        return false;
      }
    } catch (e) {
      _setError('خطأ في حذف الوجبة: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Toggle meal availability
  Future<bool> toggleMealAvailability(String mealId, bool isAvailable) async {
    try {
      final response = await RestaurantService.toggleMealAvailability(
        mealId: mealId,
        isAvailable: isAvailable,
      );

      if (response['success']) {
        // Update meal in local list
        final mealIndex = _meals.indexWhere((meal) => meal['id'] == mealId);
        if (mealIndex != -1) {
          _meals[mealIndex]['is_available'] = isAvailable;
          notifyListeners();
        }
        return true;
      } else {
        _setError(response['message']);
        return false;
      }
    } catch (e) {
      _setError('خطأ في تحديث توفر الوجبة: $e');
      return false;
    }
  }

  // =====================================================
  // UTILITY METHODS
  // الطرق المساعدة
  // =====================================================

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }

  /// Get meals by category
  List<Map<String, dynamic>> getMealsByCategory(String categoryId) {
    return _meals.where((meal) => meal['category_id'] == categoryId).toList();
  }

  /// Get available meals only
  List<Map<String, dynamic>> getAvailableMeals() {
    return _meals.where((meal) => meal['is_available'] == true).toList();
  }

  /// Search meals
  List<Map<String, dynamic>> searchMeals(String query) {
    if (query.isEmpty) return _meals;
    
    final lowerQuery = query.toLowerCase();
    return _meals.where((meal) {
      final name = meal['name']?.toString().toLowerCase() ?? '';
      final description = meal['description']?.toString().toLowerCase() ?? '';
      return name.contains(lowerQuery) || description.contains(lowerQuery);
    }).toList();
  }

  /// Refresh all data
  Future<void> refresh() async {
    await initializeRestaurant();
  }
}
