// =====================================================
// RESTAURANT SERVICE
// خدمة المطاعم ونقاط البيع
// =====================================================

import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../../core/config/supabase_config.dart';
import '../../../../core/services/database_service.dart';

class RestaurantService {
  // =====================================================
  // RESTAURANT MANAGEMENT
  // إدارة المطاعم
  // =====================================================

  /// Get restaurant by user ID
  static Future<Map<String, dynamic>> getRestaurantByUserId(String userId) async {
    try {
      final response = await DatabaseService.read(
        table: SupabaseConfig.restaurantsTable,
        filters: {'user_id': userId},
      );

      if (response['success'] && response['data'].isNotEmpty) {
        return SupabaseConfig.successResponse(
          data: response['data'][0],
          message: 'تم جلب بيانات المطعم بنجاح',
        );
      } else {
        return SupabaseConfig.errorResponse(
          message: 'لم يتم العثور على المطعم',
        );
      }
    } catch (e) {
      return SupabaseConfig.errorResponse(
        message: 'خطأ في جلب بيانات المطعم',
        error: e.toString(),
      );
    }
  }

  /// Create new restaurant
  static Future<Map<String, dynamic>> createRestaurant({
    required String userId,
    required String name,
    required String email,
    required String phone,
    required String address,
    String? description,
    List<String>? cuisineType,
    double deliveryFee = 15.0,
    double minimumOrder = 50.0,
    int estimatedDeliveryTime = 30,
    Map<String, dynamic>? openingHours,
  }) async {
    try {
      final restaurantData = {
        'user_id': userId,
        'name': name,
        'email': email,
        'phone': phone,
        'address': address,
        'description': description,
        'cuisine_type': cuisineType ?? [],
        'delivery_fee': deliveryFee,
        'minimum_order': minimumOrder,
        'estimated_delivery_time': estimatedDeliveryTime,
        'opening_hours': openingHours ?? {},
        'is_active': false, // Needs admin approval
        'is_open': false,
        'is_verified': false,
        'rating': 5.0,
        'total_ratings': 0,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      return await DatabaseService.create(
        table: SupabaseConfig.restaurantsTable,
        data: restaurantData,
      );
    } catch (e) {
      return SupabaseConfig.errorResponse(
        message: 'فشل في إنشاء المطعم',
        error: e.toString(),
      );
    }
  }

  /// Update restaurant information
  static Future<Map<String, dynamic>> updateRestaurant({
    required String restaurantId,
    required Map<String, dynamic> data,
  }) async {
    try {
      data['updated_at'] = DateTime.now().toIso8601String();

      return await DatabaseService.update(
        table: SupabaseConfig.restaurantsTable,
        data: data,
        filters: {'id': restaurantId},
      );
    } catch (e) {
      return SupabaseConfig.errorResponse(
        message: 'فشل في تحديث بيانات المطعم',
        error: e.toString(),
      );
    }
  }

  /// Toggle restaurant status (open/closed)
  static Future<Map<String, dynamic>> toggleRestaurantStatus({
    required String restaurantId,
    required bool isOpen,
  }) async {
    try {
      return await DatabaseService.update(
        table: SupabaseConfig.restaurantsTable,
        data: {
          'is_open': isOpen,
          'updated_at': DateTime.now().toIso8601String(),
        },
        filters: {'id': restaurantId},
      );
    } catch (e) {
      return SupabaseConfig.errorResponse(
        message: 'فشل في تحديث حالة المطعم',
        error: e.toString(),
      );
    }
  }

  // =====================================================
  // MENU MANAGEMENT
  // إدارة القوائم
  // =====================================================

  /// Get restaurant categories
  static Future<Map<String, dynamic>> getCategories() async {
    try {
      return await DatabaseService.read(
        table: SupabaseConfig.categoriesTable,
        orderBy: 'sort_order',
        ascending: true,
      );
    } catch (e) {
      return SupabaseConfig.errorResponse(
        message: 'فشل في جلب الفئات',
        error: e.toString(),
      );
    }
  }

  /// Get restaurant meals
  static Future<Map<String, dynamic>> getRestaurantMeals({
    required String restaurantId,
    String? categoryId,
    bool? isAvailable,
    String? searchQuery,
  }) async {
    try {
      Map<String, dynamic> filters = {'restaurant_id': restaurantId};
      
      if (categoryId != null) {
        filters['category_id'] = categoryId;
      }
      
      if (isAvailable != null) {
        filters['is_available'] = isAvailable;
      }

      var response = await DatabaseService.read(
        table: SupabaseConfig.mealsTable,
        select: '''
          *,
          categories (
            id,
            name,
            name_en
          )
        ''',
        filters: filters,
        orderBy: 'sort_order',
        ascending: true,
      );

      // Apply search filter if provided
      if (searchQuery != null && searchQuery.isNotEmpty && response['success']) {
        final meals = response['data'] as List;
        final filteredMeals = meals.where((meal) {
          final name = meal['name']?.toString().toLowerCase() ?? '';
          final description = meal['description']?.toString().toLowerCase() ?? '';
          final query = searchQuery.toLowerCase();
          return name.contains(query) || description.contains(query);
        }).toList();

        response['data'] = filteredMeals;
      }

      return response;
    } catch (e) {
      return SupabaseConfig.errorResponse(
        message: 'فشل في جلب الوجبات',
        error: e.toString(),
      );
    }
  }

  /// Add new meal
  static Future<Map<String, dynamic>> addMeal({
    required String restaurantId,
    required String categoryId,
    required String name,
    required String description,
    required double price,
    String? nameEn,
    String? descriptionEn,
    double? originalPrice,
    int discountPercentage = 0,
    List<String>? ingredients,
    List<String>? allergens,
    Map<String, dynamic>? nutritionalInfo,
    int preparationTime = 15,
    int? calories,
    bool isAvailable = true,
    bool isFeatured = false,
    bool isSpicy = false,
    bool isVegetarian = false,
    bool isVegan = false,
    bool isHalal = true,
    List<String>? tags,
    int sortOrder = 0,
  }) async {
    try {
      final mealData = {
        'restaurant_id': restaurantId,
        'category_id': categoryId,
        'name': name,
        'name_en': nameEn,
        'description': description,
        'description_en': descriptionEn,
        'price': price,
        'original_price': originalPrice,
        'discount_percentage': discountPercentage,
        'ingredients': ingredients ?? [],
        'allergens': allergens ?? [],
        'nutritional_info': nutritionalInfo ?? {},
        'preparation_time': preparationTime,
        'calories': calories,
        'is_available': isAvailable,
        'is_featured': isFeatured,
        'is_spicy': isSpicy,
        'is_vegetarian': isVegetarian,
        'is_vegan': isVegan,
        'is_halal': isHalal,
        'tags': tags ?? [],
        'sort_order': sortOrder,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      return await DatabaseService.create(
        table: SupabaseConfig.mealsTable,
        data: mealData,
      );
    } catch (e) {
      return SupabaseConfig.errorResponse(
        message: 'فشل في إضافة الوجبة',
        error: e.toString(),
      );
    }
  }

  /// Update meal
  static Future<Map<String, dynamic>> updateMeal({
    required String mealId,
    required Map<String, dynamic> data,
  }) async {
    try {
      data['updated_at'] = DateTime.now().toIso8601String();

      return await DatabaseService.update(
        table: SupabaseConfig.mealsTable,
        data: data,
        filters: {'id': mealId},
      );
    } catch (e) {
      return SupabaseConfig.errorResponse(
        message: 'فشل في تحديث الوجبة',
        error: e.toString(),
      );
    }
  }

  /// Delete meal
  static Future<Map<String, dynamic>> deleteMeal(String mealId) async {
    try {
      return await DatabaseService.delete(
        table: SupabaseConfig.mealsTable,
        filters: {'id': mealId},
      );
    } catch (e) {
      return SupabaseConfig.errorResponse(
        message: 'فشل في حذف الوجبة',
        error: e.toString(),
      );
    }
  }

  /// Toggle meal availability
  static Future<Map<String, dynamic>> toggleMealAvailability({
    required String mealId,
    required bool isAvailable,
  }) async {
    try {
      return await DatabaseService.update(
        table: SupabaseConfig.mealsTable,
        data: {
          'is_available': isAvailable,
          'updated_at': DateTime.now().toIso8601String(),
        },
        filters: {'id': mealId},
      );
    } catch (e) {
      return SupabaseConfig.errorResponse(
        message: 'فشل في تحديث توفر الوجبة',
        error: e.toString(),
      );
    }
  }

  // =====================================================
  // STATISTICS AND ANALYTICS
  // الإحصائيات والتحليلات
  // =====================================================

  /// Get restaurant statistics
  static Future<Map<String, dynamic>> getRestaurantStatistics({
    required String restaurantId,
    String period = 'today',
  }) async {
    try {
      DateTime startDate;
      final now = DateTime.now();

      switch (period) {
        case 'today':
          startDate = DateTime(now.year, now.month, now.day);
          break;
        case 'week':
          startDate = now.subtract(const Duration(days: 7));
          break;
        case 'month':
          startDate = DateTime(now.year, now.month, 1);
          break;
        default:
          startDate = DateTime(now.year, now.month, now.day);
      }

      // Get orders for the period
      final ordersResponse = await DatabaseService.getWithFilters(
        table: SupabaseConfig.ordersTable,
        equals: {'restaurant_id': restaurantId},
        greaterThan: {'created_at': startDate.toIso8601String()},
      );

      if (!ordersResponse['success']) {
        return ordersResponse;
      }

      final orders = ordersResponse['data'] as List;

      // Calculate statistics
      final totalOrders = orders.length;
      final pendingOrders = orders.where((o) => o['status'] == 'pending').length;
      final confirmedOrders = orders.where((o) => o['status'] == 'confirmed').length;
      final preparingOrders = orders.where((o) => o['status'] == 'preparing').length;
      final readyOrders = orders.where((o) => o['status'] == 'ready').length;
      final completedOrders = orders.where((o) => o['status'] == 'delivered').length;
      final cancelledOrders = orders.where((o) => o['status'] == 'cancelled').length;

      final totalRevenue = orders
          .where((o) => o['status'] == 'delivered')
          .fold<double>(0.0, (sum, o) => sum + (o['total_price'] ?? 0.0));

      final averageOrderValue = completedOrders > 0 ? totalRevenue / completedOrders : 0.0;

      return SupabaseConfig.successResponse(
        data: {
          'total_orders': totalOrders,
          'pending_orders': pendingOrders,
          'confirmed_orders': confirmedOrders,
          'preparing_orders': preparingOrders,
          'ready_orders': readyOrders,
          'completed_orders': completedOrders,
          'cancelled_orders': cancelledOrders,
          'total_revenue': totalRevenue,
          'average_order_value': averageOrderValue,
          'period': period,
          'start_date': startDate.toIso8601String(),
          'end_date': now.toIso8601String(),
        },
        message: 'تم جلب الإحصائيات بنجاح',
      );
    } catch (e) {
      return SupabaseConfig.errorResponse(
        message: 'فشل في جلب الإحصائيات',
        error: e.toString(),
      );
    }
  }
}
