# دليل اختبار المصادقة - Wssalti POS

## 🔧 حل مشكلة 400 Bad Request

تم إنشاء شاشة اختبار خاصة لتشخيص وحل مشاكل المصادقة مع Supabase.

---

## 🎯 الهدف من شاشة الاختبار

### المشاكل التي تحلها:
- ✅ تشخيص مشاكل الاتصال مع Supabase
- ✅ اختبار إنشاء الحساب بأبسط طريقة ممكنة
- ✅ عرض رسائل الخطأ التفصيلية
- ✅ التحقق من صحة إعدادات قاعدة البيانات

---

## 🚀 كيفية الوصول لشاشة الاختبار

### الطريقة 1: من شاشة تسجيل الدخول
1. افتح التطبيق: `http://localhost:8080`
2. في أسفل شاشة تسجيل الدخول
3. اضغط على "اختبار المصادقة (للمطورين)"

### الطريقة 2: الوصول المباشر
```
http://localhost:8080/#/test-auth
```

---

## 🧪 الاختبارات المتوفرة

### 1. اختبار الاتصال مع Supabase
**الغرض:** التأكد من أن التطبيق يتصل بـ Supabase بنجاح

**كيفية الاستخدام:**
1. اضغط "اختبار الاتصال مع Supabase"
2. راقب النتيجة في المربع السفلي

**النتائج المتوقعة:**
- ✅ **نجح:** `الاتصال بـ Supabase يعمل`
- ❌ **فشل:** `فشل الاتصال بـ Supabase` + تفاصيل الخطأ

### 2. اختبار إنشاء حساب مبسط
**الغرض:** إنشاء حساب بأقل البيانات المطلوبة

**كيفية الاستخدام:**
1. أدخل بريد إلكتروني صحيح
2. أدخل كلمة مرور (6 أحرف على الأقل)
3. اضغط "اختبار إنشاء حساب"

**النتائج المتوقعة:**
- ✅ **نجح:** `تم إنشاء الحساب بنجاح!` + معرف المستخدم
- ❌ **فشل:** رسالة خطأ مفصلة

### 3. اختبار تسجيل الدخول
**الغرض:** تسجيل الدخول بحساب موجود

**كيفية الاستخدام:**
1. أدخل بريد إلكتروني لحساب موجود
2. أدخل كلمة المرور الصحيحة
3. اضغط "اختبار تسجيل دخول"

---

## 🔍 تشخيص الأخطاء

### خطأ 400 - Bad Request
**الأسباب المحتملة:**
1. **بيانات غير صحيحة** - البريد الإلكتروني أو كلمة المرور
2. **إعدادات Supabase خاطئة** - URL أو API Key
3. **قيود في قاعدة البيانات** - سياسات الأمان

**الحلول:**
1. تحقق من صحة البريد الإلكتروني
2. تأكد من أن كلمة المرور 6 أحرف على الأقل
3. راجع إعدادات Supabase في `supabase_config.dart`

### خطأ 422 - Unprocessable Entity
**السبب:** البريد الإلكتروني مسجل مسبقاً

**الحل:** استخدم بريد إلكتروني مختلف

### خطأ في الاتصال
**الأسباب:**
- مشكلة في الإنترنت
- خطأ في URL الخاص بـ Supabase
- مشكلة في الخادم

---

## 📋 أمثلة على الاختبار

### مثال 1: اختبار ناجح
```
البريد الإلكتروني: <EMAIL>
كلمة المرور: 123456

النتيجة:
{
  success: true,
  message: تم إنشاء الحساب بنجاح!,
  user_id: 12345678-1234-1234-1234-123456789012,
  email: <EMAIL>
}
```

### مثال 2: خطأ في البيانات
```
البريد الإلكتروني: invalid-email
كلمة المرور: 123

النتيجة:
{
  success: false,
  message: البريد الإلكتروني غير صحيح
}
```

### مثال 3: بريد مسجل مسبقاً
```
البريد الإلكتروني: <EMAIL>
كلمة المرور: 123456

النتيجة:
{
  success: false,
  message: البريد الإلكتروني مسجل مسبقاً,
  error_code: 422
}
```

---

## 🛠️ الملفات المضافة

### 1. خدمة الاختبار
**الملف:** `lib/core/services/test_auth_service.dart`

**الميزات:**
- اختبار الاتصال
- إنشاء حساب مبسط
- تسجيل دخول مبسط
- معالجة أخطاء مفصلة

### 2. شاشة الاختبار
**الملف:** `lib/features/auth/presentation/screens/test_auth_screen.dart`

**الميزات:**
- واجهة اختبار سهلة
- عرض النتائج التفصيلية
- أزرار اختبار منفصلة
- رسائل واضحة

---

## 🔄 الخطوات التالية

### بعد نجاح الاختبارات:
1. **استخدم الحساب الجديد** في شاشة تسجيل الدخول العادية
2. **انتقل للتطوير** - أضف المزيد من الميزات
3. **احذف شاشة الاختبار** عند الانتهاء من التطوير

### في حالة فشل الاختبارات:
1. **راجع إعدادات Supabase** في لوحة التحكم
2. **تحقق من سياسات الأمان** (RLS Policies)
3. **تأكد من تفعيل المصادقة** في Supabase
4. **راجع الشبكة والاتصال**

---

## 🔐 إعدادات Supabase المطلوبة

### في لوحة تحكم Supabase:
1. **تفعيل المصادقة:**
   - Authentication → Settings
   - Enable email confirmations: OFF (للاختبار)
   - Enable phone confirmations: OFF

2. **سياسات الأمان:**
   - Database → Policies
   - تأكد من وجود سياسات للجدول `auth.users`

3. **إعدادات البريد الإلكتروني:**
   - Authentication → Settings → SMTP Settings
   - يمكن تعطيلها للاختبار

---

## 📞 الدعم والمساعدة

### في حالة استمرار المشاكل:
1. **راجع console المتصفح** للأخطاء التفصيلية
2. **تحقق من Network tab** في أدوات المطور
3. **راجع logs Supabase** في لوحة التحكم
4. **استخدم Postman** لاختبار API مباشرة

### الملفات المرجعية:
- `FIXED_ISSUES.md` - المشاكل المحلولة
- `QUICK_START.md` - دليل البدء السريع
- `supabase_config.dart` - إعدادات قاعدة البيانات

---

**🎯 الهدف: تشخيص وحل مشاكل المصادقة بطريقة منهجية**

استخدم شاشة الاختبار لفهم المشكلة بدقة، ثم طبق الحلول المناسبة.
