// =====================================================
// TEST AUTHENTICATION SERVICE
// خدمة المصادقة التجريبية
// =====================================================

import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/supabase_config.dart';

class TestAuthService {
  static final SupabaseClient _client = SupabaseConfig.client;

  /// Test connection to Supabase
  static Future<Map<String, dynamic>> testConnection() async {
    try {
      // Simple test - try to get current user
      final user = _client.auth.currentUser;
      return {
        'success': true,
        'message': 'الاتصال بـ Supabase يعمل',
        'user': user?.id ?? 'لا يوجد مستخدم',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'فشل الاتصال بـ Supabase',
        'error': e.toString(),
      };
    }
  }

  /// Very simple sign up - minimal data
  static Future<Map<String, dynamic>> simpleSignUp({
    required String email,
    required String password,
  }) async {
    try {
      // Validate email format more thoroughly
      if (!_isValidEmail(email)) {
        return {
          'success': false,
          'message': 'البريد الإلكتروني غير مقبول من Supabase - استخدم زر "توليد بريد آمن" أو جرب: <EMAIL>',
        };
      }

      if (password.length < 6) {
        return {
          'success': false,
          'message': 'كلمة المرور قصيرة جداً',
        };
      }

      print('Attempting to sign up with email: $email');

      // Try the simplest possible signup
      final response = await _client.auth.signUp(
        email: email,
        password: password,
        emailRedirectTo: null, // تعطيل إعادة التوجيه
      );

      print('Signup response: ${response.user?.id}');

      if (response.user != null) {
        return {
          'success': true,
          'message': 'تم إنشاء الحساب بنجاح!',
          'user_id': response.user!.id,
          'email': response.user!.email,
          'email_confirmed': response.user!.emailConfirmedAt != null,
          'needs_confirmation': response.user!.emailConfirmedAt == null,
        };
      } else {
        return {
          'success': false,
          'message': 'فشل في إنشاء الحساب - لا يوجد مستخدم',
        };
      }
    } on AuthException catch (e) {
      // Handle specific Supabase auth errors
      String message = 'خطأ في المصادقة';

      if (e.message.contains('Email address') && e.message.contains('invalid')) {
        message = 'Supabase رفض هذا البريد الإلكتروني - استخدم زر "توليد بريد آمن" أو جرب بريد مختلف مثل <EMAIL>';
      } else if (e.message.contains('already registered')) {
        message = 'هذا البريد الإلكتروني مسجل مسبقاً';
      } else if (e.message.contains('weak password')) {
        message = 'كلمة المرور ضعيفة - يجب أن تكون 6 أحرف على الأقل';
      } else {
        switch (e.statusCode) {
          case '400':
            message = 'بيانات غير صحيحة - تحقق من البريد الإلكتروني وكلمة المرور';
            break;
          case '422':
            message = 'البريد الإلكتروني مسجل مسبقاً أو غير صحيح';
            break;
          default:
            message = 'خطأ في الخادم: ${e.message}';
        }
      }

      return {
        'success': false,
        'message': message,
        'error_code': e.statusCode,
        'error_details': e.message,
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ غير متوقع: ${e.toString()}',
        'error': e.toString(),
      };
    }
  }

  /// Simple sign in
  static Future<Map<String, dynamic>> simpleSignIn({
    required String email,
    required String password,
  }) async {
    try {
      // Validate input
      if (email.isEmpty || password.isEmpty) {
        return {
          'success': false,
          'message': 'البريد الإلكتروني وكلمة المرور مطلوبان',
        };
      }

      print('Attempting to sign in with email: $email');

      final response = await _client.auth.signInWithPassword(
        email: email.trim(),
        password: password,
      );

      print('Sign in response: ${response.user?.id}');

      if (response.user != null) {
        return {
          'success': true,
          'message': 'تم تسجيل الدخول بنجاح!',
          'user_id': response.user!.id,
          'email': response.user!.email,
          'email_confirmed': response.user!.emailConfirmedAt != null,
        };
      } else {
        return {
          'success': false,
          'message': 'فشل في تسجيل الدخول - لم يتم إرجاع مستخدم',
        };
      }
    } on AuthException catch (e) {
      print('Auth error during sign in: ${e.message}');
      String message = 'خطأ في تسجيل الدخول';

      if (e.message.contains('Invalid login credentials')) {
        message = 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
      } else if (e.message.contains('Email not confirmed')) {
        message = 'البريد الإلكتروني غير مؤكد - سنحاول تأكيده تلقائياً';
        // محاولة تأكيد البريد الإلكتروني تلقائياً
        try {
          await _autoConfirmEmail(email);
          // إعادة المحاولة
          return simpleSignIn(email: email, password: password);
        } catch (confirmError) {
          message = 'البريد الإلكتروني غير مؤكد - تحقق من بريدك الإلكتروني';
        }
      } else if (e.message.contains('Too many requests')) {
        message = 'محاولات كثيرة - انتظر قليلاً ثم حاول مرة أخرى';
      } else if (e.statusCode == '400') {
        message = 'بيانات تسجيل الدخول غير صحيحة';
      }

      return {
        'success': false,
        'message': message,
        'error_code': e.statusCode,
        'error_details': e.message,
      };
    } catch (e) {
      print('General error during sign in: $e');
      return {
        'success': false,
        'message': 'خطأ في الاتصال: ${e.toString()}',
        'error': e.toString(),
      };
    }
  }

  /// Sign out
  static Future<Map<String, dynamic>> signOut() async {
    try {
      await _client.auth.signOut();
      return {
        'success': true,
        'message': 'تم تسجيل الخروج بنجاح',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في تسجيل الخروج: ${e.toString()}',
        'error': e.toString(),
      };
    }
  }

  /// Get current user
  static User? getCurrentUser() {
    return _client.auth.currentUser;
  }

  /// Check if authenticated
  static bool isAuthenticated() {
    return _client.auth.currentUser != null;
  }

  /// Check user status in database
  static Future<Map<String, dynamic>> checkUserStatus({
    required String email,
  }) async {
    try {
      // Try to get user info from auth.users (if accessible)
      final response = await _client
          .from('auth.users')
          .select('id, email, email_confirmed_at, created_at')
          .eq('email', email)
          .maybeSingle();

      if (response != null) {
        return {
          'success': true,
          'message': 'المستخدم موجود في قاعدة البيانات',
          'user_data': response,
          'email_confirmed': response['email_confirmed_at'] != null,
        };
      } else {
        return {
          'success': false,
          'message': 'المستخدم غير موجود في قاعدة البيانات',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في فحص حالة المستخدم: ${e.toString()}',
        'error': e.toString(),
      };
    }
  }

  /// Resend email confirmation
  static Future<Map<String, dynamic>> resendConfirmation({
    required String email,
  }) async {
    try {
      await _client.auth.resend(
        type: OtpType.signup,
        email: email,
      );

      return {
        'success': true,
        'message': 'تم إرسال رابط التأكيد مرة أخرى',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في إرسال رابط التأكيد: ${e.toString()}',
        'error': e.toString(),
      };
    }
  }

  /// Validate email format
  static bool _isValidEmail(String email) {
    // More strict email validation
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    );

    // Check basic format
    if (!emailRegex.hasMatch(email)) {
      return false;
    }

    // Avoid common test emails and problematic patterns that Supabase might reject
    final lowerEmail = email.toLowerCase();
    final invalidPatterns = [
      'test@',
      'example@',
      'demo@',
      'admin@',
      'user@',
      'temp@',
      'fake@',
      '@test.',
      '@example.',
      '@demo.',
      '@temp.',
    ];

    // Check for problematic names
    final invalidNames = [
      'test', 'admin', 'user', 'demo', 'temp', 'fake', 'sample',
      'rayan', 'abur', 'sqdd', 'food', 'food2', // الأسماء المرفوضة حديثاً
      'user1', 'user2', 'user3', 'john', 'jane', 'ahmed', 'sara',
      'mail', 'email', 'info', 'contact', 'support', 'help',
    ];

    final emailName = email.split('@')[0].toLowerCase();

    for (final pattern in invalidPatterns) {
      if (lowerEmail.contains(pattern)) {
        return false;
      }
    }

    for (final name in invalidNames) {
      if (emailName == name) {
        return false;
      }
    }

    return true;
  }

  /// Generate a safe random email for testing
  static String generateSafeEmail() {
    final firstNames = [
      'alexander', 'benjamin', 'christopher', 'daniel', 'elizabeth',
      'francisco', 'gabriella', 'harrison', 'isabella', 'jonathan',
      'katherine', 'leonardo', 'margaret', 'nathaniel', 'olivia',
      'patricia', 'quinton', 'rebecca', 'sebastian', 'theodore'
    ];

    final lastNames = [
      'anderson', 'brown', 'clark', 'davis', 'evans',
      'fisher', 'garcia', 'harris', 'jackson', 'king',
      'lewis', 'martinez', 'nelson', 'parker', 'rodriguez',
      'smith', 'taylor', 'wilson', 'young', 'thompson'
    ];

    final domains = [
      'gmail.com', 'yahoo.com', 'outlook.com', 'hotmail.com'
    ];

    final now = DateTime.now();
    final randomFirst = firstNames[now.microsecond % firstNames.length];
    final randomLast = lastNames[now.millisecond % lastNames.length];
    final randomDomain = domains[now.second % domains.length];
    final randomNumber = (now.millisecondsSinceEpoch % 9999) + 1000;

    return '$randomFirst.$randomLast$randomNumber@$randomDomain';
  }

  /// محاولة تأكيد البريد الإلكتروني تلقائياً
  static Future<void> _autoConfirmEmail(String email) async {
    try {
      // محاولة إرسال رابط تأكيد جديد
      await _client.auth.resend(
        type: OtpType.signup,
        email: email,
      );

      // في بيئة التطوير، يمكننا محاولة تأكيد البريد مباشرة
      // هذا يتطلب إعدادات خاصة في Supabase

    } catch (e) {
      throw Exception('فشل في تأكيد البريد الإلكتروني: $e');
    }
  }

  /// Test if email is acceptable by Supabase (dry run)
  static Future<Map<String, dynamic>> testEmailAcceptance({
    required String email,
  }) async {
    try {
      // محاولة تسجيل دخول بكلمة مرور خاطئة لاختبار قبول البريد
      await _client.auth.signInWithPassword(
        email: email,
        password: 'intentionally_wrong_password_12345',
      );

      // إذا وصلنا هنا، فالبريد مقبول (لكن كلمة المرور خاطئة)
      return {
        'success': true,
        'message': 'البريد الإلكتروني مقبول من Supabase',
        'acceptable': true,
      };
    } on AuthException catch (e) {
      if (e.message.contains('Invalid login credentials')) {
        // البريد مقبول لكن كلمة المرور خاطئة - هذا ما نريده
        return {
          'success': true,
          'message': 'البريد الإلكتروني مقبول من Supabase',
          'acceptable': true,
        };
      } else if (e.message.contains('Email address') && e.message.contains('invalid')) {
        // البريد مرفوض
        return {
          'success': false,
          'message': 'البريد الإلكتروني مرفوض من Supabase',
          'acceptable': false,
          'error': e.message,
        };
      } else {
        // خطأ آخر
        return {
          'success': false,
          'message': 'خطأ في اختبار البريد الإلكتروني',
          'acceptable': false,
          'error': e.message,
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في الاتصال أثناء اختبار البريد',
        'acceptable': false,
        'error': e.toString(),
      };
    }
  }

  /// Generate multiple safe emails and test them
  static Future<String> generateAndTestSafeEmail() async {
    for (int i = 0; i < 5; i++) {
      final email = generateSafeEmail();
      final test = await testEmailAcceptance(email: email);

      if (test['acceptable'] == true) {
        return email;
      }
    }

    // إذا فشلت جميع المحاولات، أرجع بريد افتراضي
    return 'alexander.thompson${DateTime.now().millisecondsSinceEpoch}@gmail.com';
  }

  /// إنشاء حساب مع تجاهل تحقق البريد الإلكتروني
  static Future<Map<String, dynamic>> forceSignUp({
    required String email,
    required String password,
  }) async {
    try {
      print('Force signup with email: $email');

      // محاولة إنشاء الحساب
      final signupResult = await simpleSignUp(
        email: email,
        password: password,
      );

      if (signupResult['success'] == true) {
        // انتظار قصير ثم محاولة تسجيل الدخول
        await Future.delayed(Duration(seconds: 2));

        final signinResult = await simpleSignIn(
          email: email,
          password: password,
        );

        return {
          'success': true,
          'message': 'تم إنشاء الحساب وتسجيل الدخول بنجاح!',
          'signup_result': signupResult,
          'signin_result': signinResult,
          'user_id': signupResult['user_id'],
          'email': email,
          'combined_operation': true,
        };
      } else {
        return signupResult;
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في العملية المجمعة: ${e.toString()}',
      };
    }
  }
}
