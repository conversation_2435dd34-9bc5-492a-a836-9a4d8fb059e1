import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../theme/app_theme.dart';

class TermsConditionsScreen extends StatelessWidget {
  const TermsConditionsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark ? AppTheme.darkBackground : AppTheme.lightBackground,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: isDark ? Colors.white : AppTheme.darkBackground,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Terms & Conditions',
          style: GoogleFonts.inter(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: isDark ? Colors.white : AppTheme.darkBackground,
          ),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: isDark ? AppTheme.darkCard : Colors.white,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: isDark ? AppTheme.darkBorder : AppTheme.lightBorder,
                ),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.description_outlined,
                    size: 48,
                    color: AppTheme.primaryGreen,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Terms & Conditions',
                    style: GoogleFonts.inter(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: isDark ? Colors.white : AppTheme.darkBackground,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Last updated: ${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}',
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      color: isDark ? Colors.grey[400] : Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Terms Content
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: isDark ? AppTheme.darkCard : Colors.white,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: isDark ? AppTheme.darkBorder : AppTheme.lightBorder,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSection(
                    '1. Acceptance of Terms',
                    'By accessing and using the Wssalti POS system, you accept and agree to be bound by the terms and provision of this agreement.',
                    isDark,
                  ),
                  
                  _buildSection(
                    '2. Use License',
                    'Permission is granted to temporarily use the Wssalti POS system for restaurant operations. This license shall automatically terminate if you violate any of these restrictions.',
                    isDark,
                  ),
                  
                  _buildSection(
                    '3. Data Protection',
                    'We are committed to protecting your data. All customer information and transaction data are encrypted and stored securely in compliance with data protection regulations.',
                    isDark,
                  ),
                  
                  _buildSection(
                    '4. Service Availability',
                    'While we strive to maintain 99.9% uptime, we cannot guarantee uninterrupted service. Scheduled maintenance will be communicated in advance.',
                    isDark,
                  ),
                  
                  _buildSection(
                    '5. Payment Processing',
                    'All payment transactions are processed securely. We are not responsible for any issues arising from third-party payment processors.',
                    isDark,
                  ),
                  
                  _buildSection(
                    '6. User Responsibilities',
                    'Users are responsible for maintaining the confidentiality of their account credentials and for all activities that occur under their account.',
                    isDark,
                  ),
                  
                  _buildSection(
                    '7. Limitation of Liability',
                    'In no event shall Wssalti be liable for any damages arising out of the use or inability to use the POS system.',
                    isDark,
                  ),
                  
                  _buildSection(
                    '8. Modifications',
                    'We reserve the right to modify these terms at any time. Users will be notified of significant changes.',
                    isDark,
                  ),
                  
                  _buildSection(
                    '9. Contact Information',
                    'For questions about these Terms & Conditions, please contact <NAME_EMAIL> or through the app support section.',
                    isDark,
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 32),
            
            // Accept Button
            SizedBox(
              width: double.infinity,
              height: 56,
              child: ElevatedButton(
                onPressed: () => Navigator.pop(context, true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryGreen,
                  foregroundColor: Colors.white,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
                child: Text(
                  'I Accept Terms & Conditions',
                  style: GoogleFonts.inter(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, String content, bool isDark) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: GoogleFonts.inter(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: isDark ? Colors.white : AppTheme.darkBackground,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: GoogleFonts.inter(
              fontSize: 14,
              height: 1.5,
              color: isDark ? Colors.grey[300] : Colors.grey[700],
            ),
          ),
        ],
      ),
    );
  }
}
