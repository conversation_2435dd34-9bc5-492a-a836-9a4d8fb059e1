// =====================================================
// TEST LOGIN CREDENTIALS
// بيانات تسجيل الدخول التجريبية
// =====================================================

/*
  Test Login Credentials for Wssalti POS System
  
  Email: <EMAIL>
  Password: Admin123!
  
  Email: <EMAIL>
  Password: Restaurant123!
  
  Email: <EMAIL>
  Password: Manager123!
  
  Note: These are test credentials for development purposes only.
  In production, use proper authentication and secure passwords.
*/

import 'package:flutter/material.dart';
import 'lib/core/services/auth_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Test creating accounts
  await createTestAccounts();
}

Future<void> createTestAccounts() async {
  print('Creating test accounts...');
  
  // Admin account
  final adminResult = await AuthService.signUpWithEmail(
    email: '<EMAIL>',
    password: 'Admin123!',
    fullName: 'System Administrator',
    role: 'admin',
    additionalData: {
      'phone': '+************',
      'department': 'IT',
    },
  );
  
  print('Admin account: ${adminResult['success'] ? 'Created' : 'Failed'}');
  if (!adminResult['success']) {
    print('Error: ${adminResult['message']}');
  }
  
  // Restaurant account
  final restaurantResult = await AuthService.signUpWithEmail(
    email: '<EMAIL>',
    password: 'Restaurant123!',
    fullName: 'Restaurant Manager',
    role: 'restaurant',
    additionalData: {
      'phone': '+************',
      'restaurant_name': 'Wssalti Restaurant',
    },
  );
  
  print('Restaurant account: ${restaurantResult['success'] ? 'Created' : 'Failed'}');
  if (!restaurantResult['success']) {
    print('Error: ${restaurantResult['message']}');
  }
  
  // Manager account
  final managerResult = await AuthService.signUpWithEmail(
    email: '<EMAIL>',
    password: 'Manager123!',
    fullName: 'Operations Manager',
    role: 'manager',
    additionalData: {
      'phone': '+************',
      'department': 'Operations',
    },
  );
  
  print('Manager account: ${managerResult['success'] ? 'Created' : 'Failed'}');
  if (!managerResult['success']) {
    print('Error: ${managerResult['message']}');
  }
  
  print('Test accounts creation completed!');
}
