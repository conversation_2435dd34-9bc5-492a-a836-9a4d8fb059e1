// =====================================================
// SUPABASE CONFIGURATION
// إعدادات قاعدة بيانات Supabase
// =====================================================

import 'package:supabase_flutter/supabase_flutter.dart';

class SupabaseConfig {
  // Supabase Configuration
  static const String supabaseUrl = 'https://pjrauaticucldzfvtoua.supabase.co';
  static const String supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.U7BVtH_YBu6wWH6n4X9QzkU0-UrpHNhsY9nI2KLXH8k';

  // Initialize Supabase
  static Future<void> initialize() async {
    await Supabase.initialize(
      url: supabaseUrl,
      anonKey: supabaseAnonKey,
      debug: true, // Set to false in production
    );
  }

  // Get Supabase client instance
  static SupabaseClient get client => Supabase.instance.client;

  // Get current user
  static User? get currentUser => client.auth.currentUser;

  // Check if user is authenticated
  static bool get isAuthenticated => currentUser != null;

  // Database table names
  static const String usersTable = 'users';
  static const String restaurantsTable = 'restaurants';
  static const String categoriesTable = 'categories';
  static const String mealsTable = 'meals';
  static const String ordersTable = 'orders';
  static const String orderItemsTable = 'order_items';
  static const String paymentsTable = 'payments';
  static const String driversTable = 'drivers';
  static const String reviewsTable = 'reviews';
  static const String notificationsTable = 'notifications';
  static const String walletsTable = 'wallets';
  static const String walletTransactionsTable = 'wallet_transactions';
  static const String promotionsTable = 'promotions';
  static const String inventoryItemsTable = 'inventory_items';
  static const String supportTicketsTable = 'support_tickets';
  static const String systemSettingsTable = 'system_settings';

  // Storage buckets
  static const String restaurantImagesBucket = 'restaurant-images';
  static const String mealImagesBucket = 'meal-images';
  static const String userAvatarsBucket = 'user-avatars';
  static const String receiptsBucket = 'receipts';

  // Real-time channels
  static const String ordersChannel = 'orders_channel';
  static const String notificationsChannel = 'notifications_channel';
  static const String driversChannel = 'drivers_channel';

  // Error messages
  static const String connectionError = 'فشل في الاتصال بقاعدة البيانات';
  static const String authError = 'خطأ في المصادقة';
  static const String permissionError = 'ليس لديك صلاحية للوصول';
  static const String notFoundError = 'البيانات غير موجودة';
  static const String validationError = 'بيانات غير صحيحة';

  // Success messages
  static const String createSuccess = 'تم الإنشاء بنجاح';
  static const String updateSuccess = 'تم التحديث بنجاح';
  static const String deleteSuccess = 'تم الحذف بنجاح';
  static const String uploadSuccess = 'تم رفع الملف بنجاح';

  // API Response helper
  static Map<String, dynamic> successResponse({
    required dynamic data,
    String message = 'تمت العملية بنجاح',
  }) {
    return {
      'success': true,
      'data': data,
      'message': message,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  static Map<String, dynamic> errorResponse({
    required String message,
    String? error,
    int? code,
  }) {
    return {
      'success': false,
      'message': message,
      'error': error,
      'code': code,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  // Database query helpers
  static String get currentUserId {
    final user = currentUser;
    if (user == null) throw Exception('المستخدم غير مسجل الدخول');
    return user.id;
  }

  // File upload helpers
  static String generateFileName(String originalName, String folder) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final extension = originalName.split('.').last;
    return '$folder/${timestamp}_${DateTime.now().microsecond}.$extension';
  }

  // Real-time subscription helpers
  static RealtimeChannel createChannel(String channelName) {
    return client.channel(channelName);
  }

  // Batch operations
  static Future<List<Map<String, dynamic>>> batchSelect(
    List<String> tables,
    String select,
  ) async {
    final futures = tables.map((table) => 
      client.from(table).select(select)
    ).toList();
    
    final results = await Future.wait(futures);
    return results.map((result) => result as Map<String, dynamic>).toList();
  }

  // Connection status
  static Future<bool> checkConnection() async {
    try {
      await client.from(systemSettingsTable).select('id').limit(1);
      return true;
    } catch (e) {
      return false;
    }
  }

  // Cleanup resources
  static Future<void> dispose() async {
    await client.dispose();
  }
}
