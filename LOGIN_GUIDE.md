# دليل تسجيل الدخول - Wssalti POS System

## نظرة عامة
تم إنشاء لوحة تسجيل الدخول للنظام مع التكامل الكامل مع قاعدة بيانات Supabase.

## الميزات المتوفرة

### 1. تسجيل الدخول بالبريد الإلكتروني
- إدخال البريد الإلكتروني أو رقم الهاتف
- كلمة المرور مع إمكانية إظهار/إخفاء
- خيار "تذكرني"
- التحقق من صحة البيانات

### 2. إعادة تعيين كلمة المرور
- إرسال رابط إعادة التعيين عبر البريد الإلكتروني
- رسائل تأكيد واضحة

### 3. تسجيل الدخول عبر وسائل التواصل الاجتماعي
- Google
- Facebook  
- Apple

### 4. واجهة مستخدم متجاوبة
- تصميم حديث ومتجاوب
- دعم الوضع الفاتح والداكن
- رسائل خطأ ونجاح واضحة
- مؤشر تحميل أثناء المعالجة

## بيانات تسجيل الدخول التجريبية

### حساب المدير
- **البريد الإلكتروني:** <EMAIL>
- **كلمة المرور:** Admin123!
- **الدور:** مدير النظام

### حساب المطعم
- **البريد الإلكتروني:** <EMAIL>
- **كلمة المرور:** Restaurant123!
- **الدور:** مدير مطعم

### حساب المدير التشغيلي
- **البريد الإلكتروني:** <EMAIL>
- **كلمة المرور:** Manager123!
- **الدور:** مدير العمليات

## كيفية الاستخدام

### 1. تشغيل التطبيق
```bash
flutter run -d chrome --web-port=8080
```

### 2. الوصول للتطبيق
افتح المتصفح على: `http://localhost:8080`

### 3. تسجيل الدخول
1. أدخل البريد الإلكتروني وكلمة المرور
2. اضغط على زر "Login"
3. سيتم توجيهك إلى لوحة التحكم عند نجاح تسجيل الدخول

### 4. إعادة تعيين كلمة المرور
1. أدخل البريد الإلكتروني
2. اضغط على "Forgot Password?"
3. تحقق من بريدك الإلكتروني للحصول على رابط إعادة التعيين

## الملفات المحدثة

### 1. خدمة المصادقة
- `lib/core/services/auth_service.dart`
- تسجيل الدخول بالبريد الإلكتروني
- تسجيل الدخول عبر وسائل التواصل الاجتماعي
- إعادة تعيين كلمة المرور
- إدارة الجلسات

### 2. شاشة تسجيل الدخول
- `lib/features/auth/presentation/screens/login_screen.dart`
- واجهة مستخدم محدثة
- معالجة الأخطاء
- مؤشرات التحميل
- التحقق من صحة البيانات

### 3. التوجيه
- `lib/main.dart`
- تحديث المسار الافتراضي لتسجيل الدخول

## الأمان

### التشفير
- كلمات المرور مشفرة بواسطة Supabase
- جلسات آمنة مع JWT tokens
- حماية من CSRF attacks

### التحقق
- التحقق من صحة البريد الإلكتروني
- قوة كلمة المرور
- حماية من محاولات تسجيل الدخول المتكررة

## المشاكل الشائعة وحلولها

### 1. خطأ في الاتصال بقاعدة البيانات
- تأكد من صحة إعدادات Supabase
- تحقق من الاتصال بالإنترنت

### 2. فشل تسجيل الدخول
- تأكد من صحة البريد الإلكتروني وكلمة المرور
- تحقق من تفعيل الحساب

### 3. عدم وصول بريد إعادة التعيين
- تحقق من مجلد الرسائل غير المرغوب فيها
- تأكد من صحة البريد الإلكتروني

## التطوير المستقبلي

### الميزات المخططة
- [ ] تسجيل الدخول بالرقم والرمز
- [ ] المصادقة الثنائية (2FA)
- [ ] تسجيل الدخول بالبصمة
- [ ] دعم اللغة العربية الكامل
- [ ] تذكر الجهاز

### التحسينات
- [ ] تحسين الأداء
- [ ] تحسين تجربة المستخدم
- [ ] إضافة المزيد من خيارات التخصيص
- [ ] تحسين الأمان

## الدعم الفني
للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.
