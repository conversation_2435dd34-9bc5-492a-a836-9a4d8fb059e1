// =====================================================
// DATABASE SERVICE
// خدمة قاعدة البيانات الأساسية
// =====================================================

import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/supabase_config.dart';

class DatabaseService {
  static final SupabaseClient _client = SupabaseConfig.client;

  // =====================================================
  // GENERIC CRUD OPERATIONS
  // العمليات الأساسية العامة
  // =====================================================

  /// Create a new record
  static Future<Map<String, dynamic>> create({
    required String table,
    required Map<String, dynamic> data,
  }) async {
    try {
      final response = await _client
          .from(table)
          .insert(data)
          .select()
          .single();

      return SupabaseConfig.successResponse(
        data: response,
        message: SupabaseConfig.createSuccess,
      );
    } catch (e) {
      return SupabaseConfig.errorResponse(
        message: 'فشل في إنشاء السجل',
        error: e.toString(),
      );
    }
  }

  /// Read records with optional filters
  static Future<Map<String, dynamic>> read({
    required String table,
    String select = '*',
    Map<String, dynamic>? filters,
    String? orderBy,
    bool ascending = true,
    int? limit,
    int? offset,
  }) async {
    try {
      dynamic query = _client.from(table).select(select);

      // Apply filters
      if (filters != null) {
        filters.forEach((key, value) {
          if (value is List) {
            query = query.inFilter(key, value);
          } else {
            query = query.eq(key, value);
          }
        });
      }

      // Apply ordering
      if (orderBy != null) {
        query = query.order(orderBy, ascending: ascending);
      }

      // Apply pagination
      if (limit != null) {
        query = query.limit(limit);
      }
      if (offset != null) {
        query = query.range(offset, offset + (limit ?? 50) - 1);
      }

      final response = await query;

      return SupabaseConfig.successResponse(
        data: response,
        message: 'تم جلب البيانات بنجاح',
      );
    } catch (e) {
      return SupabaseConfig.errorResponse(
        message: 'فشل في جلب البيانات',
        error: e.toString(),
      );
    }
  }

  /// Update a record
  static Future<Map<String, dynamic>> update({
    required String table,
    required Map<String, dynamic> data,
    required Map<String, dynamic> filters,
  }) async {
    try {
      dynamic query = _client.from(table).update(data);

      // Apply filters
      filters.forEach((key, value) {
        query = query.eq(key, value);
      });

      final response = await query.select().single();

      return SupabaseConfig.successResponse(
        data: response,
        message: SupabaseConfig.updateSuccess,
      );
    } catch (e) {
      return SupabaseConfig.errorResponse(
        message: 'فشل في تحديث السجل',
        error: e.toString(),
      );
    }
  }

  /// Delete a record
  static Future<Map<String, dynamic>> delete({
    required String table,
    required Map<String, dynamic> filters,
  }) async {
    try {
      dynamic query = _client.from(table).delete();

      // Apply filters
      filters.forEach((key, value) {
        query = query.eq(key, value);
      });

      await query;

      return SupabaseConfig.successResponse(
        data: null,
        message: SupabaseConfig.deleteSuccess,
      );
    } catch (e) {
      return SupabaseConfig.errorResponse(
        message: 'فشل في حذف السجل',
        error: e.toString(),
      );
    }
  }

  // =====================================================
  // SEARCH AND FILTER OPERATIONS
  // عمليات البحث والفلترة
  // =====================================================

  /// Search records with text query
  static Future<Map<String, dynamic>> search({
    required String table,
    required String column,
    required String query,
    String select = '*',
    int? limit,
  }) async {
    try {
      dynamic searchQuery = _client
          .from(table)
          .select(select)
          .ilike(column, '%$query%');

      if (limit != null) {
        searchQuery = searchQuery.limit(limit);
      }

      final response = await searchQuery;

      return SupabaseConfig.successResponse(
        data: response,
        message: 'تم البحث بنجاح',
      );
    } catch (e) {
      return SupabaseConfig.errorResponse(
        message: 'فشل في البحث',
        error: e.toString(),
      );
    }
  }

  /// Get records with complex filters
  static Future<Map<String, dynamic>> getWithFilters({
    required String table,
    String select = '*',
    Map<String, dynamic>? equals,
    Map<String, List<dynamic>>? inFilters,
    Map<String, dynamic>? greaterThan,
    Map<String, dynamic>? lessThan,
    Map<String, String>? textSearch,
    String? orderBy,
    bool ascending = true,
    int? limit,
  }) async {
    try {
      dynamic query = _client.from(table).select(select);

      // Equal filters
      if (equals != null) {
        equals.forEach((key, value) {
          query = query.eq(key, value);
        });
      }

      // In filters
      if (inFilters != null) {
        inFilters.forEach((key, values) {
          query = query.inFilter(key, values);
        });
      }

      // Greater than filters
      if (greaterThan != null) {
        greaterThan.forEach((key, value) {
          query = query.gt(key, value);
        });
      }

      // Less than filters
      if (lessThan != null) {
        lessThan.forEach((key, value) {
          query = query.lt(key, value);
        });
      }

      // Text search filters
      if (textSearch != null) {
        textSearch.forEach((key, value) {
          query = query.ilike(key, '%$value%');
        });
      }

      // Ordering
      if (orderBy != null) {
        query = query.order(orderBy, ascending: ascending);
      }

      // Limit
      if (limit != null) {
        query = query.limit(limit);
      }

      final response = await query;

      return SupabaseConfig.successResponse(
        data: response,
        message: 'تم جلب البيانات بنجاح',
      );
    } catch (e) {
      return SupabaseConfig.errorResponse(
        message: 'فشل في جلب البيانات',
        error: e.toString(),
      );
    }
  }

  // =====================================================
  // AGGREGATION OPERATIONS
  // عمليات التجميع والإحصائيات
  // =====================================================

  /// Count records
  static Future<Map<String, dynamic>> count({
    required String table,
    Map<String, dynamic>? filters,
  }) async {
    try {
      dynamic query = _client.from(table).select('*', const FetchOptions(count: CountOption.exact));

      // Apply filters
      if (filters != null) {
        filters.forEach((key, value) {
          query = query.eq(key, value);
        });
      }

      final response = await query;
      final count = response.count ?? 0;

      return SupabaseConfig.successResponse(
        data: {'count': count},
        message: 'تم حساب العدد بنجاح',
      );
    } catch (e) {
      return SupabaseConfig.errorResponse(
        message: 'فشل في حساب العدد',
        error: e.toString(),
      );
    }
  }

  // =====================================================
  // REAL-TIME SUBSCRIPTIONS
  // الاشتراكات الفورية
  // =====================================================

  /// Subscribe to table changes
  static RealtimeChannel subscribeToTable({
    required String table,
    required Function(PostgresChangePayload) onInsert,
    required Function(PostgresChangePayload) onUpdate,
    required Function(PostgresChangePayload) onDelete,
  }) {
    final channel = _client
        .channel('public:$table')
        .onPostgresChanges(
          event: PostgresChangeEvent.insert,
          schema: 'public',
          table: table,
          callback: onInsert,
        )
        .onPostgresChanges(
          event: PostgresChangeEvent.update,
          schema: 'public',
          table: table,
          callback: onUpdate,
        )
        .onPostgresChanges(
          event: PostgresChangeEvent.delete,
          schema: 'public',
          table: table,
          callback: onDelete,
        );

    channel.subscribe();
    return channel;
  }

  // =====================================================
  // BATCH OPERATIONS
  // العمليات المجمعة
  // =====================================================

  /// Insert multiple records
  static Future<Map<String, dynamic>> batchInsert({
    required String table,
    required List<Map<String, dynamic>> data,
  }) async {
    try {
      final response = await _client
          .from(table)
          .insert(data)
          .select();

      return SupabaseConfig.successResponse(
        data: response,
        message: 'تم إدراج السجلات بنجاح',
      );
    } catch (e) {
      return SupabaseConfig.errorResponse(
        message: 'فشل في إدراج السجلات',
        error: e.toString(),
      );
    }
  }

  /// Update multiple records
  static Future<Map<String, dynamic>> batchUpdate({
    required String table,
    required Map<String, dynamic> data,
    required Map<String, List<dynamic>> filters,
  }) async {
    try {
      dynamic query = _client.from(table).update(data);

      // Apply filters
      filters.forEach((key, values) {
        query = query.inFilter(key, values);
      });

      final response = await query.select();

      return SupabaseConfig.successResponse(
        data: response,
        message: 'تم تحديث السجلات بنجاح',
      );
    } catch (e) {
      return SupabaseConfig.errorResponse(
        message: 'فشل في تحديث السجلات',
        error: e.toString(),
      );
    }
  }
}
