import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/services/test_auth_service.dart';

class TestAuthScreen extends StatefulWidget {
  const TestAuthScreen({super.key});

  @override
  State<TestAuthScreen> createState() => _TestAuthScreenState();
}

class _TestAuthScreenState extends State<TestAuthScreen> {
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isLoading = false;
  String _result = '';

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _testConnection() async {
    setState(() {
      _isLoading = true;
      _result = 'جاري اختبار الاتصال...';
    });

    final result = await TestAuthService.testConnection();
    
    setState(() {
      _isLoading = false;
      _result = 'نتيجة اختبار الاتصال:\n${result.toString()}';
    });
  }

  Future<void> _testSignUp() async {
    if (_emailController.text.isEmpty || _passwordController.text.isEmpty) {
      setState(() {
        _result = 'يرجى إدخال البريد الإلكتروني وكلمة المرور';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _result = 'جاري إنشاء الحساب...';
    });

    final result = await TestAuthService.simpleSignUp(
      email: _emailController.text.trim(),
      password: _passwordController.text,
    );
    
    setState(() {
      _isLoading = false;
      _result = 'نتيجة إنشاء الحساب:\n${result.toString()}';
    });
  }

  Future<void> _testSignIn() async {
    if (_emailController.text.isEmpty || _passwordController.text.isEmpty) {
      setState(() {
        _result = 'يرجى إدخال البريد الإلكتروني وكلمة المرور';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _result = 'جاري تسجيل الدخول...';
    });

    final result = await TestAuthService.simpleSignIn(
      email: _emailController.text.trim(),
      password: _passwordController.text,
    );

    setState(() {
      _isLoading = false;
      _result = 'نتيجة تسجيل الدخول:\n${result.toString()}';
    });
  }

  Future<void> _checkUserStatus() async {
    if (_emailController.text.isEmpty) {
      setState(() {
        _result = 'يرجى إدخال البريد الإلكتروني';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _result = 'جاري فحص حالة المستخدم...';
    });

    final result = await TestAuthService.checkUserStatus(
      email: _emailController.text.trim(),
    );

    setState(() {
      _isLoading = false;
      _result = 'حالة المستخدم:\n${result.toString()}';
    });
  }

  Future<void> _resendConfirmation() async {
    if (_emailController.text.isEmpty) {
      setState(() {
        _result = 'يرجى إدخال البريد الإلكتروني';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _result = 'جاري إرسال رابط التأكيد...';
    });

    final result = await TestAuthService.resendConfirmation(
      email: _emailController.text.trim(),
    );

    setState(() {
      _isLoading = false;
      _result = 'نتيجة إرسال التأكيد:\n${result.toString()}';
    });
  }

  Future<void> _testEmailAcceptance() async {
    if (_emailController.text.isEmpty) {
      setState(() {
        _result = 'يرجى إدخال البريد الإلكتروني';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _result = 'جاري اختبار قبول البريد الإلكتروني...';
    });

    final result = await TestAuthService.testEmailAcceptance(
      email: _emailController.text.trim(),
    );

    setState(() {
      _isLoading = false;
      _result = 'نتيجة اختبار البريد:\n${result.toString()}';
    });
  }

  Future<void> _generateAndTestEmail() async {
    setState(() {
      _isLoading = true;
      _result = 'جاري توليد واختبار بريد آمن...';
    });

    final email = await TestAuthService.generateAndTestSafeEmail();

    setState(() {
      _emailController.text = email;
      _isLoading = false;
      _result = 'تم توليد بريد مختبر: $email';
    });
  }

  Future<void> _forceSignUp() async {
    if (_emailController.text.isEmpty || _passwordController.text.isEmpty) {
      setState(() {
        _result = 'يرجى إدخال البريد الإلكتروني وكلمة المرور';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _result = 'جاري إنشاء الحساب وتسجيل الدخول...';
    });

    final result = await TestAuthService.forceSignUp(
      email: _emailController.text.trim(),
      password: _passwordController.text,
    );

    setState(() {
      _isLoading = false;
      _result = 'نتيجة العملية المجمعة:\n${result.toString()}';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'اختبار المصادقة',
          style: GoogleFonts.inter(
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Connection Test
            ElevatedButton(
              onPressed: _isLoading ? null : _testConnection,
              child: Text('اختبار الاتصال مع Supabase'),
            ),
            
            const SizedBox(height: 24),
            
            // Email Field
            TextField(
              controller: _emailController,
              decoration: const InputDecoration(
                labelText: 'البريد الإلكتروني',
                hintText: '<EMAIL>',
                helperText: 'استخدم بريد حقيقي - تجنب test@ أو example@',
                prefixIcon: Icon(Icons.email),
              ),
              keyboardType: TextInputType.emailAddress,
            ),
            
            const SizedBox(height: 8),

            // Email Action Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : () {
                      _emailController.text = TestAuthService.generateSafeEmail();
                    },
                    icon: const Icon(Icons.refresh, size: 16),
                    label: Text(
                      'توليد بريد',
                      style: GoogleFonts.inter(fontSize: 12),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.success.withValues(alpha: 0.1),
                      foregroundColor: AppColors.success,
                      elevation: 0,
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : _generateAndTestEmail,
                    icon: const Icon(Icons.verified, size: 16),
                    label: Text(
                      'توليد مختبر',
                      style: GoogleFonts.inter(fontSize: 12),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary.withValues(alpha: 0.1),
                      foregroundColor: AppColors.primary,
                      elevation: 0,
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : _testEmailAcceptance,
                    icon: const Icon(Icons.check_circle, size: 16),
                    label: Text(
                      'اختبار البريد',
                      style: GoogleFonts.inter(fontSize: 12),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.warning.withValues(alpha: 0.1),
                      foregroundColor: AppColors.warning,
                      elevation: 0,
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            // Email Suggestions
            Wrap(
              spacing: 8,
              children: [
                _buildEmailSuggestion('<EMAIL>'),
                _buildEmailSuggestion('<EMAIL>'),
                _buildEmailSuggestion('<EMAIL>'),
                _buildEmailSuggestion('<EMAIL>'),
              ],
            ),

            const SizedBox(height: 16),

            // Password Field
            TextField(
              controller: _passwordController,
              decoration: const InputDecoration(
                labelText: 'كلمة المرور',
                hintText: '123456',
                prefixIcon: Icon(Icons.lock),
              ),
              obscureText: true,
            ),
            
            const SizedBox(height: 24),
            
            // Sign Up Test
            ElevatedButton(
              onPressed: _isLoading ? null : _testSignUp,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.success,
              ),
              child: Text('اختبار إنشاء حساب'),
            ),
            
            const SizedBox(height: 16),
            
            // Sign In Test
            ElevatedButton(
              onPressed: _isLoading ? null : _testSignIn,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.info,
              ),
              child: Text('اختبار تسجيل دخول'),
            ),

            const SizedBox(height: 16),

            // Force Sign Up (Combined Operation)
            ElevatedButton(
              onPressed: _isLoading ? null : _forceSignUp,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.success,
              ),
              child: Text('إنشاء حساب + تسجيل دخول'),
            ),

            const SizedBox(height: 16),

            // Check User Status
            ElevatedButton(
              onPressed: _isLoading ? null : _checkUserStatus,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.warning,
              ),
              child: Text('فحص حالة المستخدم'),
            ),

            const SizedBox(height: 16),

            // Resend Confirmation
            ElevatedButton(
              onPressed: _isLoading ? null : _resendConfirmation,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.secondary,
              ),
              child: Text('إعادة إرسال تأكيد البريد'),
            ),
            
            const SizedBox(height: 24),
            
            // Loading Indicator
            if (_isLoading)
              const Center(
                child: CircularProgressIndicator(),
              ),
            
            const SizedBox(height: 16),
            
            // Result Display
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppColors.greyLight,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppColors.inputBorder),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _result.isEmpty ? 'النتائج ستظهر هنا...' : _result,
                    style: GoogleFonts.robotoMono(
                      fontSize: 14,
                    ),
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Back to Login
            TextButton(
              onPressed: () {
                Navigator.of(context).pushReplacementNamed('/login');
              },
              child: Text(
                'العودة لشاشة تسجيل الدخول',
                style: GoogleFonts.inter(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Build email suggestion chip
  Widget _buildEmailSuggestion(String email) {
    return ActionChip(
      label: Text(
        email,
        style: GoogleFonts.inter(fontSize: 12),
      ),
      onPressed: () {
        _emailController.text = email;
      },
      backgroundColor: AppColors.primary.withValues(alpha: 0.1),
      side: BorderSide(color: AppColors.primary.withValues(alpha: 0.3)),
    );
  }
}
