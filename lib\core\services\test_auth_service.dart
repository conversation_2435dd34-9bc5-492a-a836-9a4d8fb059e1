// =====================================================
// TEST AUTHENTICATION SERVICE
// خدمة المصادقة التجريبية
// =====================================================

import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/supabase_config.dart';

class TestAuthService {
  static final SupabaseClient _client = SupabaseConfig.client;

  /// Test connection to Supabase
  static Future<Map<String, dynamic>> testConnection() async {
    try {
      // Simple test - try to get current user
      final user = _client.auth.currentUser;
      return {
        'success': true,
        'message': 'الاتصال بـ Supabase يعمل',
        'user': user?.id ?? 'لا يوجد مستخدم',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'فشل الاتصال بـ Supabase',
        'error': e.toString(),
      };
    }
  }

  /// Very simple sign up - minimal data
  static Future<Map<String, dynamic>> simpleSignUp({
    required String email,
    required String password,
  }) async {
    try {
      // Validate email format more thoroughly
      if (!_isValidEmail(email)) {
        return {
          'success': false,
          'message': 'البريد الإلكتروني غير صحيح - استخدم بريد حقيقي مثل <EMAIL>',
        };
      }

      if (password.length < 6) {
        return {
          'success': false,
          'message': 'كلمة المرور قصيرة جداً',
        };
      }

      // Try the simplest possible signup
      final response = await _client.auth.signUp(
        email: email,
        password: password,
      );

      if (response.user != null) {
        return {
          'success': true,
          'message': 'تم إنشاء الحساب بنجاح!',
          'user_id': response.user!.id,
          'email': response.user!.email,
        };
      } else {
        return {
          'success': false,
          'message': 'فشل في إنشاء الحساب - لا يوجد مستخدم',
        };
      }
    } on AuthException catch (e) {
      // Handle specific Supabase auth errors
      String message = 'خطأ في المصادقة';

      if (e.message.contains('Email address') && e.message.contains('invalid')) {
        message = 'البريد الإلكتروني غير صحيح - استخدم بريد حقيقي مثل <EMAIL>';
      } else if (e.message.contains('already registered')) {
        message = 'هذا البريد الإلكتروني مسجل مسبقاً';
      } else if (e.message.contains('weak password')) {
        message = 'كلمة المرور ضعيفة - يجب أن تكون 6 أحرف على الأقل';
      } else {
        switch (e.statusCode) {
          case '400':
            message = 'بيانات غير صحيحة - تحقق من البريد الإلكتروني وكلمة المرور';
            break;
          case '422':
            message = 'البريد الإلكتروني مسجل مسبقاً أو غير صحيح';
            break;
          default:
            message = 'خطأ في الخادم: ${e.message}';
        }
      }

      return {
        'success': false,
        'message': message,
        'error_code': e.statusCode,
        'error_details': e.message,
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ غير متوقع: ${e.toString()}',
        'error': e.toString(),
      };
    }
  }

  /// Simple sign in
  static Future<Map<String, dynamic>> simpleSignIn({
    required String email,
    required String password,
  }) async {
    try {
      // Validate input
      if (email.isEmpty || password.isEmpty) {
        return {
          'success': false,
          'message': 'البريد الإلكتروني وكلمة المرور مطلوبان',
        };
      }

      print('Attempting to sign in with email: $email');

      final response = await _client.auth.signInWithPassword(
        email: email.trim(),
        password: password,
      );

      print('Sign in response: ${response.user?.id}');

      if (response.user != null) {
        return {
          'success': true,
          'message': 'تم تسجيل الدخول بنجاح!',
          'user_id': response.user!.id,
          'email': response.user!.email,
          'email_confirmed': response.user!.emailConfirmedAt != null,
        };
      } else {
        return {
          'success': false,
          'message': 'فشل في تسجيل الدخول - لم يتم إرجاع مستخدم',
        };
      }
    } on AuthException catch (e) {
      print('Auth error during sign in: ${e.message}');
      String message = 'خطأ في تسجيل الدخول';

      if (e.message.contains('Invalid login credentials')) {
        message = 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
      } else if (e.message.contains('Email not confirmed')) {
        message = 'يرجى تأكيد البريد الإلكتروني أولاً';
      } else if (e.message.contains('Too many requests')) {
        message = 'محاولات كثيرة - انتظر قليلاً ثم حاول مرة أخرى';
      } else if (e.statusCode == '400') {
        message = 'بيانات تسجيل الدخول غير صحيحة';
      }

      return {
        'success': false,
        'message': message,
        'error_code': e.statusCode,
        'error_details': e.message,
      };
    } catch (e) {
      print('General error during sign in: $e');
      return {
        'success': false,
        'message': 'خطأ في الاتصال: ${e.toString()}',
        'error': e.toString(),
      };
    }
  }

  /// Sign out
  static Future<Map<String, dynamic>> signOut() async {
    try {
      await _client.auth.signOut();
      return {
        'success': true,
        'message': 'تم تسجيل الخروج بنجاح',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في تسجيل الخروج: ${e.toString()}',
        'error': e.toString(),
      };
    }
  }

  /// Get current user
  static User? getCurrentUser() {
    return _client.auth.currentUser;
  }

  /// Check if authenticated
  static bool isAuthenticated() {
    return _client.auth.currentUser != null;
  }

  /// Check user status in database
  static Future<Map<String, dynamic>> checkUserStatus({
    required String email,
  }) async {
    try {
      // Try to get user info from auth.users (if accessible)
      final response = await _client
          .from('auth.users')
          .select('id, email, email_confirmed_at, created_at')
          .eq('email', email)
          .maybeSingle();

      if (response != null) {
        return {
          'success': true,
          'message': 'المستخدم موجود في قاعدة البيانات',
          'user_data': response,
          'email_confirmed': response['email_confirmed_at'] != null,
        };
      } else {
        return {
          'success': false,
          'message': 'المستخدم غير موجود في قاعدة البيانات',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في فحص حالة المستخدم: ${e.toString()}',
        'error': e.toString(),
      };
    }
  }

  /// Resend email confirmation
  static Future<Map<String, dynamic>> resendConfirmation({
    required String email,
  }) async {
    try {
      await _client.auth.resend(
        type: OtpType.signup,
        email: email,
      );

      return {
        'success': true,
        'message': 'تم إرسال رابط التأكيد مرة أخرى',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في إرسال رابط التأكيد: ${e.toString()}',
        'error': e.toString(),
      };
    }
  }

  /// Validate email format
  static bool _isValidEmail(String email) {
    // More strict email validation
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    );

    // Check basic format
    if (!emailRegex.hasMatch(email)) {
      return false;
    }

    // Avoid common test emails that Supabase might reject
    final lowerEmail = email.toLowerCase();
    final invalidPatterns = [
      'test@',
      'example@',
      'demo@',
      '@test.',
      '@example.',
      '@demo.',
    ];

    for (final pattern in invalidPatterns) {
      if (lowerEmail.contains(pattern)) {
        return false;
      }
    }

    return true;
  }
}
