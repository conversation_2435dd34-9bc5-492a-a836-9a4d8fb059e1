// =====================================================
// AUTHENTICATION SERVICE
// خدمة المصادقة والتحقق
// =====================================================

import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/supabase_config.dart';

class AuthService {
  static final SupabaseClient _client = SupabaseConfig.client;

  // =====================================================
  // AUTHENTICATION METHODS
  // طرق المصادقة
  // =====================================================

  /// Sign in with email and password
  static Future<Map<String, dynamic>> signInWithEmail({
    required String email,
    required String password,
  }) async {
    try {
      final response = await _client.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user != null) {
        // Get user profile from database
        final userProfile = await _getUserProfile(response.user!.id);
        
        return SupabaseConfig.successResponse(
          data: {
            'user': response.user,
            'session': response.session,
            'profile': userProfile,
          },
          message: 'تم تسجيل الدخول بنجاح',
        );
      } else {
        return SupabaseConfig.errorResponse(
          message: 'فشل في تسجيل الدخول',
        );
      }
    } catch (e) {
      return SupabaseConfig.errorResponse(
        message: 'خطأ في تسجيل الدخول',
        error: e.toString(),
      );
    }
  }

  /// Sign in with phone and password
  static Future<Map<String, dynamic>> signInWithPhone({
    required String phone,
    required String password,
  }) async {
    try {
      final response = await _client.auth.signInWithPassword(
        phone: phone,
        password: password,
      );

      if (response.user != null) {
        final userProfile = await _getUserProfile(response.user!.id);
        
        return SupabaseConfig.successResponse(
          data: {
            'user': response.user,
            'session': response.session,
            'profile': userProfile,
          },
          message: 'تم تسجيل الدخول بنجاح',
        );
      } else {
        return SupabaseConfig.errorResponse(
          message: 'فشل في تسجيل الدخول',
        );
      }
    } catch (e) {
      return SupabaseConfig.errorResponse(
        message: 'خطأ في تسجيل الدخول',
        error: e.toString(),
      );
    }
  }

  /// Sign up with email
  static Future<Map<String, dynamic>> signUpWithEmail({
    required String email,
    required String password,
    required String fullName,
    String role = 'restaurant',
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      final response = await _client.auth.signUp(
        email: email,
        password: password,
        data: {
          'full_name': fullName,
          'role': role,
          ...?additionalData,
        },
      );

      if (response.user != null) {
        // Create user profile in database
        await _createUserProfile(
          userId: response.user!.id,
          email: email,
          fullName: fullName,
          role: role,
          additionalData: additionalData,
        );

        return SupabaseConfig.successResponse(
          data: {
            'user': response.user,
            'session': response.session,
          },
          message: 'تم إنشاء الحساب بنجاح',
        );
      } else {
        return SupabaseConfig.errorResponse(
          message: 'فشل في إنشاء الحساب',
        );
      }
    } catch (e) {
      return SupabaseConfig.errorResponse(
        message: 'خطأ في إنشاء الحساب',
        error: e.toString(),
      );
    }
  }

  /// Sign out
  static Future<Map<String, dynamic>> signOut() async {
    try {
      await _client.auth.signOut();
      
      return SupabaseConfig.successResponse(
        data: null,
        message: 'تم تسجيل الخروج بنجاح',
      );
    } catch (e) {
      return SupabaseConfig.errorResponse(
        message: 'خطأ في تسجيل الخروج',
        error: e.toString(),
      );
    }
  }

  /// Reset password
  static Future<Map<String, dynamic>> resetPassword({
    required String email,
  }) async {
    try {
      await _client.auth.resetPasswordForEmail(email);
      
      return SupabaseConfig.successResponse(
        data: null,
        message: 'تم إرسال رابط إعادة تعيين كلمة المرور',
      );
    } catch (e) {
      return SupabaseConfig.errorResponse(
        message: 'فشل في إرسال رابط إعادة التعيين',
        error: e.toString(),
      );
    }
  }

  /// Update password
  static Future<Map<String, dynamic>> updatePassword({
    required String newPassword,
  }) async {
    try {
      final response = await _client.auth.updateUser(
        UserAttributes(password: newPassword),
      );

      return SupabaseConfig.successResponse(
        data: response.user,
        message: 'تم تحديث كلمة المرور بنجاح',
      );
    } catch (e) {
      return SupabaseConfig.errorResponse(
        message: 'فشل في تحديث كلمة المرور',
        error: e.toString(),
      );
    }
  }

  // =====================================================
  // USER PROFILE METHODS
  // طرق ملف المستخدم
  // =====================================================

  /// Get current user
  static User? getCurrentUser() {
    return _client.auth.currentUser;
  }

  /// Get current session
  static Session? getCurrentSession() {
    return _client.auth.currentSession;
  }

  /// Check if user is authenticated
  static bool isAuthenticated() {
    return _client.auth.currentUser != null;
  }

  /// Get user profile from database
  static Future<Map<String, dynamic>?> _getUserProfile(String userId) async {
    try {
      final response = await _client
          .from(SupabaseConfig.usersTable)
          .select('*')
          .eq('id', userId)
          .single();

      return response;
    } catch (e) {
      print('Error getting user profile: $e');
      return null;
    }
  }

  /// Create user profile in database
  static Future<void> _createUserProfile({
    required String userId,
    required String email,
    required String fullName,
    required String role,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      await _client.from(SupabaseConfig.usersTable).insert({
        'id': userId,
        'email': email,
        'full_name': fullName,
        'role': role,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
        ...?additionalData,
      });
    } catch (e) {
      print('Error creating user profile: $e');
    }
  }

  /// Update user profile
  static Future<Map<String, dynamic>> updateProfile({
    required Map<String, dynamic> data,
  }) async {
    try {
      final user = getCurrentUser();
      if (user == null) {
        return SupabaseConfig.errorResponse(
          message: 'المستخدم غير مسجل الدخول',
        );
      }

      // Update auth user metadata
      if (data.containsKey('full_name') || data.containsKey('avatar_url')) {
        await _client.auth.updateUser(
          UserAttributes(
            data: {
              if (data.containsKey('full_name')) 'full_name': data['full_name'],
              if (data.containsKey('avatar_url')) 'avatar_url': data['avatar_url'],
            },
          ),
        );
      }

      // Update user profile in database
      final response = await _client
          .from(SupabaseConfig.usersTable)
          .update({
            ...data,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', user.id)
          .select()
          .single();

      return SupabaseConfig.successResponse(
        data: response,
        message: 'تم تحديث الملف الشخصي بنجاح',
      );
    } catch (e) {
      return SupabaseConfig.errorResponse(
        message: 'فشل في تحديث الملف الشخصي',
        error: e.toString(),
      );
    }
  }

  // =====================================================
  // AUTH STATE MANAGEMENT
  // إدارة حالة المصادقة
  // =====================================================

  /// Listen to auth state changes
  static Stream<AuthState> get authStateChanges {
    return _client.auth.onAuthStateChange;
  }

  /// Get current user profile with restaurant data
  static Future<Map<String, dynamic>?> getCurrentUserWithRestaurant() async {
    try {
      final user = getCurrentUser();
      if (user == null) return null;

      final response = await _client
          .from(SupabaseConfig.usersTable)
          .select('''
            *,
            restaurants (*)
          ''')
          .eq('id', user.id)
          .single();

      return response;
    } catch (e) {
      print('Error getting user with restaurant: $e');
      return null;
    }
  }

  /// Check user role
  static Future<String?> getUserRole() async {
    try {
      final user = getCurrentUser();
      if (user == null) return null;

      final response = await _client
          .from(SupabaseConfig.usersTable)
          .select('role')
          .eq('id', user.id)
          .single();

      return response['role'];
    } catch (e) {
      print('Error getting user role: $e');
      return null;
    }
  }

  /// Check if user has specific role
  static Future<bool> hasRole(String role) async {
    final userRole = await getUserRole();
    return userRole == role;
  }

  /// Check if user is restaurant owner
  static Future<bool> isRestaurantOwner() async {
    return await hasRole('restaurant');
  }

  /// Check if user is admin
  static Future<bool> isAdmin() async {
    return await hasRole('admin');
  }
}
