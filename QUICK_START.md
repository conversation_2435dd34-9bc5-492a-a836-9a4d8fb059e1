# دليل البدء السريع - Wssalti POS

## 🚀 تشغيل التطبيق

### 1. تشغيل التطبيق
```bash
flutter run -d chrome --web-port=8080
```

### 2. فتح التطبيق
افتح المتصفح على: **http://localhost:8080**

## 🔐 تسجيل الدخول

### بيانات تجريبية جاهزة:

#### حساب المدير
- **البريد:** `<EMAIL>`
- **كلمة المرور:** `Admin123!`

#### حساب المطعم  
- **البريد:** `<EMAIL>`
- **كلمة المرور:** `Restaurant123!`

### خطوات تسجيل الدخول:
1. أدخل البريد الإلكتروني
2. أدخل كلمة المرور
3. اضغط "Login"
4. سيتم توجيهك للوحة التحكم

## ✨ الميزات المتوفرة

### ✅ تم إنجازه:
- [x] لوحة تسجيل دخول احترافية
- [x] شاشة إنشاء حساب متكاملة
- [x] تكامل مع قاعدة بيانات Supabase
- [x] تسجيل دخول بالبريد الإلكتروني
- [x] إعادة تعيين كلمة المرور
- [x] تسجيل دخول اجتماعي (Google, Facebook, Apple)
- [x] دعم أنواع حسابات متعددة (مطعم، مدير، كاشير)
- [x] واجهة مستخدم متجاوبة
- [x] رسائل خطأ ونجاح واضحة
- [x] مؤشرات تحميل
- [x] التحقق من صحة البيانات
- [x] إصلاح مشاكل قاعدة البيانات

### 🔄 قيد التطوير:
- [ ] لوحة التحكم الرئيسية
- [ ] إدارة الطلبات
- [ ] إدارة القائمة
- [ ] نظام الفواتير
- [ ] تقارير المبيعات

## 🛠️ المشاكل الشائعة

### مشكلة: التطبيق لا يعمل
**الحل:**
```bash
flutter clean
flutter pub get
flutter run -d chrome
```

### مشكلة: خطأ في تسجيل الدخول
**الحل:**
- تأكد من الاتصال بالإنترنت
- استخدم البيانات التجريبية المذكورة أعلاه
- تحقق من إعدادات Supabase

### مشكلة: الصفحة لا تظهر
**الحل:**
- تأكد من أن المنفذ 8080 غير مستخدم
- جرب منفذ آخر: `flutter run -d chrome --web-port=3000`

## 📱 الاستخدام

### إنشاء حساب جديد:
1. افتح التطبيق
2. اضغط "Sign Up Here"
3. املأ النموذج:
   - الاسم الكامل
   - البريد الإلكتروني
   - رقم الهاتف
   - نوع الحساب (مطعم/مدير/كاشير)
   - كلمة المرور
4. وافق على الشروط
5. اضغط "Create Account"

### تسجيل الدخول:
1. افتح التطبيق
2. أدخل البيانات التجريبية أو حسابك الجديد
3. اضغط "Login"

### إعادة تعيين كلمة المرور:
1. أدخل البريد الإلكتروني
2. اضغط "Forgot Password?"
3. تحقق من بريدك الإلكتروني

### تسجيل الخروج:
- سيتم إضافة هذه الميزة في لوحة التحكم

## 🎨 التصميم

### الألوان:
- **الأساسي:** أخضر (#4CAF50)
- **الثانوي:** أزرق (#2196F3)
- **الخطأ:** أحمر (#F44336)
- **النجاح:** أخضر (#4CAF50)

### الخطوط:
- **الأساسي:** Inter
- **الثانوي:** Roboto

## 📞 الدعم

للحصول على المساعدة:
1. راجع ملف `LOGIN_GUIDE.md` للتفاصيل الكاملة
2. تحقق من ملف `README.md` للمعلومات العامة
3. راجع التوثيق في مجلد `docs/`

---

**ملاحظة:** هذا نظام تجريبي للتطوير. لا تستخدم البيانات التجريبية في الإنتاج.
