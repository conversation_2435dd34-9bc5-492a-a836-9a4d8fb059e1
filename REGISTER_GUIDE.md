# دليل شاشة إنشاء الحساب - Wssalti POS

## 🎯 نظرة عامة
تم إنشاء شاشة إنشاء حساب احترافية ومتكاملة لنظام Wssalti POS مع دعم أنواع مختلفة من المستخدمين.

## ✨ الميزات المتوفرة

### 1. نموذج إنشاء الحساب الشامل
- **الاسم الكامل** - مع التحقق من صحة البيانات
- **البريد الإلكتروني** - مع التحقق من التنسيق
- **رقم الهاتف** - مع دعم الأرقام الدولية
- **نوع الحساب** - اختيار من قائمة منسدلة
- **اسم المطعم** - يظهر فقط لأصحاب المطاعم
- **كلمة المرور** - مع متطلبات الأمان
- **تأكيد كلمة المرور** - للتأكد من التطابق

### 2. أنواع الحسابات المدعومة
- **🏪 Restaurant Owner (مالك مطعم)**
  - إدارة كاملة للمطعم
  - إضافة القوائم والأطباق
  - مراقبة المبيعات والتقارير
  
- **👨‍💼 Manager (مدير)**
  - إدارة العمليات اليومية
  - مراقبة الأداء
  - إدارة الموظفين
  
- **💰 Cashier (كاشير)**
  - معالجة الطلبات
  - إدارة المدفوعات
  - طباعة الفواتير

### 3. التحقق من صحة البيانات
- **الاسم**: 2-50 حرف، عربي أو إنجليزي
- **البريد الإلكتروني**: تنسيق صحيح ومطلوب
- **الهاتف**: رقم صحيح مع رمز الدولة
- **كلمة المرور**: 
  - 6 أحرف على الأقل
  - حرف كبير واحد على الأقل
  - حرف صغير واحد على الأقل
  - رقم واحد على الأقل

### 4. واجهة المستخدم
- **تصميم متجاوب** - يعمل على جميع الأحجام
- **ألوان متناسقة** - مع هوية Wssalti
- **أيقونات واضحة** - لكل نوع حساب
- **رسائل خطأ مفيدة** - توجيه المستخدم
- **مؤشر تحميل** - أثناء إنشاء الحساب

## 🚀 كيفية الاستخدام

### 1. الوصول لشاشة إنشاء الحساب
```
من شاشة تسجيل الدخول → اضغط "Sign Up Here"
أو
الوصول المباشر: http://localhost:8080/#/register
```

### 2. ملء النموذج
1. **أدخل الاسم الكامل**
2. **أدخل البريد الإلكتروني**
3. **أدخل رقم الهاتف** (مثال: +************)
4. **اختر نوع الحساب** من القائمة المنسدلة
5. **أدخل اسم المطعم** (إذا كنت مالك مطعم)
6. **أدخل كلمة المرور**
7. **أكد كلمة المرور**
8. **وافق على الشروط والأحكام**
9. **اضغط "Create Account"**

### 3. بعد إنشاء الحساب
- سيتم عرض رسالة نجاح
- سيتم توجيهك لشاشة تسجيل الدخول
- استخدم البيانات الجديدة لتسجيل الدخول

## 🔧 الملفات المحدثة

### 1. شاشة إنشاء الحساب
**الملف:** `lib/features/auth/presentation/screens/register_screen.dart`

**المكونات:**
- نموذج إنشاء الحساب الكامل
- التحقق من صحة البيانات
- معالجة الأخطاء
- واجهة مستخدم متجاوبة

### 2. خدمة قاعدة البيانات
**الملف:** `lib/core/services/database_service.dart`

**الإصلاحات:**
- إصلاح مشاكل أنواع البيانات
- تحسين استعلامات البحث
- تحسين عمليات التحديث والحذف

### 3. التوجيه
**الملف:** `lib/core/routes/app_routes.dart`

**الإضافات:**
- مسار شاشة إنشاء الحساب `/register`
- ربط الشاشة بالتوجيه

### 4. شاشة تسجيل الدخول
**الملف:** `lib/features/auth/presentation/screens/login_screen.dart`

**التحديثات:**
- إضافة رابط لشاشة إنشاء الحساب
- تحسين التنقل بين الشاشات

## 🛡️ الأمان والحماية

### 1. تشفير البيانات
- كلمات المرور مشفرة بواسطة Supabase
- بيانات المستخدم محمية
- اتصال آمن مع قاعدة البيانات

### 2. التحقق من البيانات
- التحقق من جانب العميل والخادم
- منع إدخال بيانات ضارة
- التحقق من تنسيق البريد الإلكتروني

### 3. إدارة الجلسات
- جلسات آمنة مع JWT
- انتهاء صلاحية تلقائي
- حماية من هجمات CSRF

## 🎨 التخصيص

### الألوان المستخدمة
```dart
Primary: #4CAF50 (أخضر)
Secondary: #2196F3 (أزرق)
Error: #F44336 (أحمر)
Success: #4CAF50 (أخضر)
Warning: #FF9800 (برتقالي)
```

### الخطوط
```dart
Primary: Inter
Secondary: Roboto
```

### الأيقونات
- **مطعم**: `Icons.store_outlined`
- **مدير**: `Icons.manage_accounts_outlined`
- **كاشير**: `Icons.point_of_sale_outlined`

## 🐛 استكشاف الأخطاء

### مشكلة: فشل إنشاء الحساب
**الأسباب المحتملة:**
- البريد الإلكتروني مستخدم مسبقاً
- كلمة مرور ضعيفة
- مشكلة في الاتصال

**الحلول:**
- استخدم بريد إلكتروني مختلف
- اتبع متطلبات كلمة المرور
- تحقق من الاتصال بالإنترنت

### مشكلة: رسائل خطأ في التحقق
**الحل:**
- تأكد من ملء جميع الحقول المطلوبة
- اتبع التنسيق المطلوب لكل حقل
- تأكد من تطابق كلمتي المرور

### مشكلة: عدم ظهور حقل اسم المطعم
**الحل:**
- تأكد من اختيار "Restaurant Owner" من القائمة
- أعد تحديث الصفحة إذا لزم الأمر

## 📱 التوافق

### المتصفحات المدعومة
- ✅ Chrome (مُوصى به)
- ✅ Firefox
- ✅ Safari
- ✅ Edge

### الأجهزة
- ✅ سطح المكتب
- ✅ الأجهزة اللوحية
- ✅ الهواتف الذكية

## 🔄 التطوير المستقبلي

### الميزات المخططة
- [ ] تأكيد البريد الإلكتروني
- [ ] تسجيل دخول بالرقم والرمز
- [ ] رفع صورة الملف الشخصي
- [ ] اختيار المدينة والمنطقة
- [ ] دعم اللغة العربية الكامل

### التحسينات
- [ ] تحسين الأداء
- [ ] إضافة المزيد من التحقق
- [ ] تحسين تجربة المستخدم
- [ ] إضافة المزيد من أنواع الحسابات

---

**ملاحظة:** هذا نظام تجريبي للتطوير. تأكد من مراجعة الأمان قبل الاستخدام في الإنتاج.
