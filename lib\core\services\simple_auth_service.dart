// =====================================================
// SIMPLE AUTHENTICATION SERVICE
// خدمة المصادقة المبسطة
// =====================================================

import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/supabase_config.dart';

class SimpleAuthService {
  static final SupabaseClient _client = SupabaseConfig.client;

  /// Simple sign up with email and password
  static Future<Map<String, dynamic>> signUpWithEmail({
    required String email,
    required String password,
    String? fullName,
  }) async {
    try {
      final response = await _client.auth.signUp(
        email: email,
        password: password,
        data: fullName != null ? {'full_name': fullName} : null,
      );

      if (response.user != null) {
        return {
          'success': true,
          'message': 'تم إنشاء الحساب بنجاح',
          'user': response.user,
          'session': response.session,
        };
      } else {
        return {
          'success': false,
          'message': 'فشل في إنشاء الحساب',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في إنشاء الحساب: ${e.toString()}',
        'error': e.toString(),
      };
    }
  }

  /// Simple sign in with email and password
  static Future<Map<String, dynamic>> signInWithEmail({
    required String email,
    required String password,
  }) async {
    try {
      final response = await _client.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user != null) {
        return {
          'success': true,
          'message': 'تم تسجيل الدخول بنجاح',
          'user': response.user,
          'session': response.session,
        };
      } else {
        return {
          'success': false,
          'message': 'فشل في تسجيل الدخول',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في تسجيل الدخول: ${e.toString()}',
        'error': e.toString(),
      };
    }
  }

  /// Sign out
  static Future<Map<String, dynamic>> signOut() async {
    try {
      await _client.auth.signOut();
      return {
        'success': true,
        'message': 'تم تسجيل الخروج بنجاح',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في تسجيل الخروج: ${e.toString()}',
        'error': e.toString(),
      };
    }
  }

  /// Reset password
  static Future<Map<String, dynamic>> resetPassword({
    required String email,
  }) async {
    try {
      await _client.auth.resetPasswordForEmail(email);
      return {
        'success': true,
        'message': 'تم إرسال رابط إعادة تعيين كلمة المرور',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في إرسال رابط إعادة التعيين: ${e.toString()}',
        'error': e.toString(),
      };
    }
  }

  /// Get current user
  static User? getCurrentUser() {
    return _client.auth.currentUser;
  }

  /// Check if user is authenticated
  static bool isAuthenticated() {
    return _client.auth.currentUser != null;
  }

  /// Get current session
  static Session? getCurrentSession() {
    return _client.auth.currentSession;
  }
}
